import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  TextField,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Email as EmailIcon,
  FileCopy as DuplicateIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { DataTable, StandardDatePicker } from '../../../shared/components';
import { vendorBillService, VendorBill, VendorBillFilters, VendorBillStats } from '../../../services/vendor-bill.service';
import { formatCurrency } from '../../../shared/utils/formatters';
import StatCard from '../../../shared/components/StatCard';
import dayjs from 'dayjs';

const VendorBillsPage: React.FC = () => {
  const navigate = useNavigate();
  const [bills, setBills] = useState<VendorBill[]>([]);
  const [stats, setStats] = useState<VendorBillStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<VendorBillFilters>({});

  // Load vendor bills
  const loadVendorBills = async () => {
    try {
      setLoading(true);
      const response = await vendorBillService.getVendorBills(filters);
      setBills(response.results);
    } catch (error) {
      console.error('Failed to load vendor bills:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load statistics
  const loadStats = async () => {
    try {
      const statsData = await vendorBillService.getVendorBillStats();
      setStats(statsData);
    } catch (error) {
      console.error('Failed to load vendor bill stats:', error);
    }
  };

  useEffect(() => {
    loadVendorBills();
    loadStats();
  }, [filters]);

  const handleFilterChange = (field: keyof VendorBillFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [field]: value || undefined
    }));
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this vendor bill?')) {
      try {
        await vendorBillService.deleteVendorBill(id);
        loadVendorBills();
        loadStats();
      } catch (error) {
        console.error('Failed to delete vendor bill:', error);
      }
    }
  };

  const handleDuplicate = async (id: number) => {
    try {
      await vendorBillService.duplicateVendorBill(id);
      loadVendorBills();
    } catch (error) {
      console.error('Failed to duplicate vendor bill:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'success';
      case 'overdue': return 'error';
      case 'pending': return 'warning';
      case 'draft': return 'default';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const columns = [
    {
      field: 'bill_number',
      headerName: 'Bill Number',
      width: 150,
      renderCell: (params: any) => (
        <Typography variant="body2" fontWeight="medium">
          {params.row.bill_number || `BILL-${params.row.id}`}
        </Typography>
      ),
    },
    {
      field: 'vendor_details',
      headerName: 'Vendor',
      width: 200,
      renderCell: (params: any) => (
        <Typography variant="body2">
          {params.row.vendor_details?.display_name || 'Unknown Vendor'}
        </Typography>
      ),
    },
    {
      field: 'bill_date',
      headerName: 'Bill Date',
      width: 120,
      renderCell: (params: any) => (
        <Typography variant="body2">
          {dayjs(params.row.bill_date).format('DD/MM/YYYY')}
        </Typography>
      ),
    },
    {
      field: 'due_date',
      headerName: 'Due Date',
      width: 120,
      renderCell: (params: any) => (
        <Typography variant="body2">
          {dayjs(params.row.due_date).format('DD/MM/YYYY')}
        </Typography>
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params: any) => (
        <Chip
          label={params.row.status.toUpperCase()}
          color={getStatusColor(params.row.status) as any}
          size="small"
        />
      ),
    },
    {
      field: 'total_amount',
      headerName: 'Amount',
      width: 120,
      renderCell: (params: any) => (
        <Typography variant="body2" fontWeight="medium">
          {formatCurrency(params.row.total_amount)}
        </Typography>
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 200,
      renderCell: (params: any) => (
        <Box>
          <Tooltip title="View">
            <IconButton
              size="small"
              onClick={() => navigate(`/dashboard/purchase/vendor-bills/${params.row.id}`)}
            >
              <ViewIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Edit">
            <IconButton
              size="small"
              onClick={() => navigate(`/dashboard/purchase/vendor-bills/${params.row.id}/edit`)}
            >
              <EditIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Duplicate">
            <IconButton
              size="small"
              onClick={() => handleDuplicate(params.row.id)}
            >
              <DuplicateIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Delete">
            <IconButton
              size="small"
              onClick={() => handleDelete(params.row.id)}
              color="error"
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      ),
    },
  ];

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Vendor Bills
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/dashboard/purchase/vendor-bills/create')}
        >
          Create Bill
        </Button>
      </Box>

      {/* Statistics Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={2}>
            <StatCard
              title="Total Bills"
              value={stats.total_bills.toString()}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <StatCard
              title="Total Payables"
              value={formatCurrency(stats.total_payables)}
              color="info"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <StatCard
              title="Outstanding"
              value={formatCurrency(stats.outstanding_amount)}
              color="warning"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <StatCard
              title="Overdue"
              value={stats.overdue_count.toString()}
              color="error"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <StatCard
              title="Paid"
              value={stats.paid_count.toString()}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <StatCard
              title="Draft"
              value={stats.draft_count.toString()}
              color="default"
            />
          </Grid>
        </Grid>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                size="small"
                label="Search"
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="Search bills..."
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                size="small"
                select
                label="Status"
                value={filters.status || ''}
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <MenuItem value="">All Status</MenuItem>
                <MenuItem value="draft">Draft</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="paid">Paid</MenuItem>
                <MenuItem value="overdue">Overdue</MenuItem>
                <MenuItem value="cancelled">Cancelled</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12} md={2}>
              <StandardDatePicker
                label="From Date"
                value={filters.date_from ? dayjs(filters.date_from) : null}
                onChange={(date) => handleFilterChange('date_from', date?.format('YYYY-MM-DD'))}
                businessContext="general"
                dateFormat="DD/MM/YYYY"
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <StandardDatePicker
                label="To Date"
                value={filters.date_to ? dayjs(filters.date_to) : null}
                onChange={(date) => handleFilterChange('date_to', date?.format('YYYY-MM-DD'))}
                businessContext="general"
                dateFormat="DD/MM/YYYY"
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                variant="outlined"
                onClick={() => setFilters({})}
                fullWidth
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <DataTable
          rows={bills}
          columns={columns}
          loading={loading}
          pageSize={25}
          checkboxSelection={false}
        />
      </Card>
    </Box>
  );
};

export default VendorBillsPage;
