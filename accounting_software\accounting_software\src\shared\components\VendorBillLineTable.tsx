import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Autocomplete,
  IconButton,
  Box,
  Typography,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import FormattedCurrencyInput from './FormattedCurrencyInput';
import { formatCurrency } from '../utils/formatters';

// Vendor Bill Line Item interface
export interface VendorBillLineItem {
  id: string;
  product_id?: number | null;
  product_name?: string;
  item_description: string;
  quantity: number;
  unit_price: number;
  line_total: number;
  tax_rate?: number;
  tax_amount?: number;
  account_code?: string;
}

// Product option interface
export interface ProductOption {
  id: number;
  name: string;
  description?: string;
  cost_price?: number;
  expense_account_code?: string;
}

// Sales Tax option interface
export interface SalesTaxOption {
  id: number;
  description: string;
  rate: number;
}

export interface VendorBillLineTableProps {
  lines: VendorBillLineItem[];
  products?: ProductOption[];
  salesTaxes?: SalesTaxOption[];
  onLineChange: (lineId: string, field: string, value: any) => void;
  onAddLine: () => void;
  onRemoveLine: (lineId: string) => void;
  currencySymbol?: string;
  readOnly?: boolean;
  minLines?: number;
  tableHeight?: string;
  showTaxColumn?: boolean;
  showAccountColumn?: boolean;
}

const VendorBillLineTable: React.FC<VendorBillLineTableProps> = ({
  lines,
  products = [],
  salesTaxes = [],
  onLineChange,
  onAddLine,
  onRemoveLine,
  currencySymbol = '$',
  readOnly = false,
  minLines = 4,
  tableHeight = '400px',
  showTaxColumn = true,
  showAccountColumn = true,
}) => {
  const [columnWidths, setColumnWidths] = useState({
    product: 180,
    description: 250,
    quantity: 80,
    unitPrice: 100,
    taxRate: 80,
    amount: 100,
    account: 140,
    actions: 60,
  });

  // Calculate line total when quantity or unit price changes
  const calculateLineTotal = (quantity: number, unitPrice: number) => {
    return quantity * unitPrice;
  };

  // Calculate tax amount
  const calculateTaxAmount = (lineTotal: number, taxRate: number) => {
    return lineTotal * (taxRate / 100);
  };

  // Handle product selection
  const handleProductSelect = (lineId: string, product: ProductOption | null) => {
    if (product) {
      onLineChange(lineId, 'product_id', product.id);
      onLineChange(lineId, 'product_name', product.name);
      onLineChange(lineId, 'item_description', product.description || product.name);
      onLineChange(lineId, 'unit_price', product.cost_price || 0);
      onLineChange(lineId, 'account_code', product.expense_account_code || '5010-COGS');
      
      // Recalculate line total
      const line = lines.find(l => l.id === lineId);
      if (line) {
        const newLineTotal = calculateLineTotal(line.quantity, product.cost_price || 0);
        onLineChange(lineId, 'line_total', newLineTotal);
        
        // Recalculate tax if tax rate exists
        if (line.tax_rate) {
          const newTaxAmount = calculateTaxAmount(newLineTotal, line.tax_rate);
          onLineChange(lineId, 'tax_amount', newTaxAmount);
        }
      }
    } else {
      onLineChange(lineId, 'product_id', null);
      onLineChange(lineId, 'product_name', '');
    }
  };

  // Handle quantity change
  const handleQuantityChange = (lineId: string, quantity: number) => {
    onLineChange(lineId, 'quantity', quantity);
    
    const line = lines.find(l => l.id === lineId);
    if (line) {
      const newLineTotal = calculateLineTotal(quantity, line.unit_price);
      onLineChange(lineId, 'line_total', newLineTotal);
      
      if (line.tax_rate) {
        const newTaxAmount = calculateTaxAmount(newLineTotal, line.tax_rate);
        onLineChange(lineId, 'tax_amount', newTaxAmount);
      }
    }
  };

  // Handle unit price change
  const handleUnitPriceChange = (lineId: string, unitPrice: number) => {
    onLineChange(lineId, 'unit_price', unitPrice);
    
    const line = lines.find(l => l.id === lineId);
    if (line) {
      const newLineTotal = calculateLineTotal(line.quantity, unitPrice);
      onLineChange(lineId, 'line_total', newLineTotal);
      
      if (line.tax_rate) {
        const newTaxAmount = calculateTaxAmount(newLineTotal, line.tax_rate);
        onLineChange(lineId, 'tax_amount', newTaxAmount);
      }
    }
  };

  // Handle tax rate change
  const handleTaxRateChange = (lineId: string, taxRate: number) => {
    onLineChange(lineId, 'tax_rate', taxRate);
    
    const line = lines.find(l => l.id === lineId);
    if (line) {
      const newTaxAmount = calculateTaxAmount(line.line_total, taxRate);
      onLineChange(lineId, 'tax_amount', newTaxAmount);
    }
  };

  return (
    <Box sx={{ width: '100%', overflow: 'hidden' }}>
      <TableContainer
        component={Paper}
        sx={{
          maxHeight: tableHeight,
          border: '1px solid #dee2e6',
          borderRadius: '8px',
          '& .MuiTableCell-root': {
            borderRight: '1px solid #dee2e6',
            padding: '6px 8px',
          },
          '& .MuiTableCell-head': {
            backgroundColor: '#f8f9fa',
            fontWeight: 600,
            fontSize: '0.875rem',
            color: '#495057',
            borderBottom: '2px solid #dee2e6',
          },
        }}
      >
        <Table stickyHeader size="small" sx={{ tableLayout: 'fixed' }}>
          <TableHead>
            <TableRow>
              <TableCell sx={{ width: columnWidths.product, minWidth: 160 }}>
                Product/Service
              </TableCell>
              <TableCell sx={{ width: columnWidths.description, minWidth: 200 }}>
                Description
              </TableCell>
              <TableCell align="center" sx={{ width: columnWidths.quantity, minWidth: 70 }}>
                Qty
              </TableCell>
              <TableCell align="right" sx={{ width: columnWidths.unitPrice, minWidth: 90 }}>
                Rate
              </TableCell>
              {showAccountColumn && (
                <TableCell sx={{ width: columnWidths.account, minWidth: 120 }}>
                  Account
                </TableCell>
              )}
              {showTaxColumn && (
                <TableCell align="center" sx={{ width: columnWidths.taxRate, minWidth: 70 }}>
                  Tax %
                </TableCell>
              )}
              <TableCell align="right" sx={{ width: columnWidths.amount, minWidth: 90 }}>
                Amount
              </TableCell>
              {!readOnly && (
                <TableCell align="center" sx={{ width: columnWidths.actions, minWidth: 50 }}>
                  <Tooltip title="Add Line">
                    <IconButton size="small" onClick={onAddLine} color="primary">
                      <AddIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {lines.map((line, index) => (
              <TableRow key={line.id} hover>
                {/* Product/Service Column */}
                <TableCell>
                  <Autocomplete
                    size="small"
                    disabled={readOnly}
                    options={products}
                    getOptionLabel={(option) => option.name}
                    value={products.find(p => p.id === line.product_id) || null}
                    onChange={(_, newValue) => handleProductSelect(line.id, newValue)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder="Select product..."
                        variant="outlined"
                        size="small"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            minHeight: '36px',
                          },
                        }}
                      />
                    )}
                  />
                </TableCell>

                {/* Description Column */}
                <TableCell>
                  <TextField
                    fullWidth
                    size="small"
                    disabled={readOnly}
                    value={line.item_description}
                    onChange={(e) => onLineChange(line.id, 'item_description', e.target.value)}
                    placeholder="Enter description..."
                    multiline
                    maxRows={2}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        minHeight: '36px',
                      },
                    }}
                  />
                </TableCell>

                {/* Quantity Column */}
                <TableCell>
                  <TextField
                    fullWidth
                    size="small"
                    type="number"
                    disabled={readOnly}
                    value={line.quantity}
                    onChange={(e) => handleQuantityChange(line.id, parseFloat(e.target.value) || 0)}
                    inputProps={{ min: 0, step: 0.01 }}
                    sx={{
                      '& .MuiOutlinedInput-input': {
                        textAlign: 'center',
                      },
                    }}
                  />
                </TableCell>

                {/* Unit Price Column */}
                <TableCell>
                  <FormattedCurrencyInput
                    fullWidth
                    size="small"
                    disabled={readOnly}
                    name={`unit_price_${line.id}`}
                    value={line.unit_price}
                    onChange={(e) => handleUnitPriceChange(line.id, parseFloat(e.target.value) || 0)}
                    currencySymbol={currencySymbol}
                  />
                </TableCell>

                {/* Account Code Column */}
                {showAccountColumn && (
                  <TableCell>
                    <TextField
                      fullWidth
                      size="small"
                      disabled={readOnly}
                      value={line.account_code || ''}
                      onChange={(e) => onLineChange(line.id, 'account_code', e.target.value)}
                      placeholder="5010-COGS"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          minHeight: '36px',
                        },
                      }}
                    />
                  </TableCell>
                )}

                {/* Tax Rate Column */}
                {showTaxColumn && (
                  <TableCell>
                    <TextField
                      fullWidth
                      size="small"
                      type="number"
                      disabled={readOnly}
                      value={line.tax_rate || 0}
                      onChange={(e) => handleTaxRateChange(line.id, parseFloat(e.target.value) || 0)}
                      inputProps={{ min: 0, max: 100, step: 0.01 }}
                      sx={{
                        '& .MuiOutlinedInput-input': {
                          textAlign: 'center',
                        },
                      }}
                    />
                  </TableCell>
                )}

                {/* Amount Column */}
                <TableCell>
                  <Typography
                    variant="body2"
                    sx={{
                      textAlign: 'right',
                      fontWeight: 500,
                      padding: '8px 12px',
                      backgroundColor: '#f8f9fa',
                      borderRadius: '4px',
                      minHeight: '36px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'flex-end',
                    }}
                  >
                    {formatCurrency(line.line_total)}
                  </Typography>
                </TableCell>

                {/* Actions Column */}
                {!readOnly && (
                  <TableCell align="center">
                    <Tooltip title="Remove Line">
                      <span>
                        <IconButton
                          size="small"
                          onClick={() => onRemoveLine(line.id)}
                          disabled={lines.length <= minLines}
                          color="error"
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </span>
                    </Tooltip>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default VendorBillLineTable;
