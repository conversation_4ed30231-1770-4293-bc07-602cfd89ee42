import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Divider,
  Alert,
  Snackbar,
  Autocomplete,
} from '@mui/material';
import {
  AttachFile as AttachFileIcon,
  Close as CloseIcon,
  Print as PrintIcon,
  Repeat as RepeatIcon,
  Save as SaveIcon,
  SaveAlt as SaveAltIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import dayjs from 'dayjs';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';
import {
  JournalLineTable,
  FormDatePicker,
  StandardDatePicker,
  CurrencyDisplay,
  type JournalLineItem,
  type AccountOption
} from '../../../shared/components';
import { salesTaxService, type SalesTaxOption } from '../../../services/sales-tax.service';
import EmployeeService from '../../../services/employee.service';
import { Employee } from '../../../shared/types/employee.types';
import { customerService, type CustomerOption } from '../../../services/customer.service';

// Interfaces
interface PaymentAccount {
  id: number;
  account_name: string;
  account_number: string;
  balance: number;
  currency: string;
}

interface TDSRate {
  id: number;
  name: string;
  percentage: number;
  description: string;
  section?: string;
  tds_type?: string;
  threshold_limit?: number;
}

// Use the reusable types
type GLAccount = AccountOption;
type PaymentLine = JournalLineItem & {
  category_id: number | null;
  category_name: string;
};

interface CashPaymentData {
  payee: string;
  payment_account_id: number | null;
  payment_date: string;
  ref_no: string;
  tags: string[];
  memo: string;
  tds_rate_id: number | null;
  payment_lines: PaymentLine[];
  attachments: File[];
}

const CashPaymentForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const { currencyInfo, loading: currencyLoading } = useCurrencyInfo();
  
  // Determine mode based on URL
  const mode = id 
    ? location.pathname.includes('/view') ? 'view' 
    : location.pathname.includes('/edit') ? 'edit' 
    : 'create'
    : 'create';
  
  const isViewMode = mode === 'view';
  const isEditMode = mode === 'edit';
  const isCreateMode = mode === 'create';

  // State
  const [paymentAccounts, setPaymentAccounts] = useState<PaymentAccount[]>([]);
  const [glAccounts, setGLAccounts] = useState<GLAccount[]>([]);
  const [tdsRates, setTdsRates] = useState<TDSRate[]>([]);
  const [salesTaxes, setSalesTaxes] = useState<SalesTaxOption[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [customers, setCustomers] = useState<CustomerOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [error] = useState<string | null>(null);
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' }>({
    open: false,
    message: '',
    severity: 'success'
  });
  const [resetTableColumns, setResetTableColumns] = useState(false);

  // Form state
  const [formData, setFormData] = useState<CashPaymentData>({
    payee: '',
    payment_account_id: null,
    payment_date: dayjs().format('YYYY-MM-DD'),
    ref_no: '',
    tags: [],
    memo: '',
    tds_rate_id: null,
    payment_lines: [
      {
        id: '1',
        account_id: null,
        category_id: null,
        category_name: '',
        description: '',
        amount: 0,
      },
      {
        id: '2',
        account_id: null,
        category_id: null,
        category_name: '',
        description: '',
        amount: 0,
      },
      {
        id: '3',
        account_id: null,
        category_id: null,
        category_name: '',
        description: '',
        amount: 0,
      },
      {
        id: '4',
        account_id: null,
        category_id: null,
        category_name: '',
        description: '',
        amount: 0,
      },
    ],
    attachments: [],
  });

  const loadCustomers = async () => {
    try {
      const customerData = await customerService.getActiveCustomers();
      console.log(`Cash Payment: Loaded ${customerData.length} active customers`);
      setCustomers(customerData);
    } catch (error) {
      console.error('Error loading customers:', error);
      setSnackbar({ 
        open: true, 
        message: 'Failed to load customer list', 
        severity: 'error' 
      });
    }
  };

  const loadTransactionData = async (transactionId: string) => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      // First, get the transaction data
      const transactionResponse = await fetch(`http://localhost:8000/api/cms/transactions/${transactionId}/`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!transactionResponse.ok) {
        throw new Error('Failed to load transaction data');
      }

      const transactionData = await transactionResponse.json();
      console.log('Loaded transaction data:', transactionData);
      
      // Initialize payment lines array
      let paymentLines = [{
        id: '1',
        account_id: null,
        category_id: null,
        category_name: '',
        description: transactionData.description || `Payment to ${transactionData.vendor || transactionData.customer || transactionData.employee || ''}`,
        amount: transactionData.amount,
        sales_tax: null,
        sales_tax_description: '',
        sales_tax_rate: 0,
        sales_tax_amount: 0,
        taxable_amount: 0,
      }];

      // If there's a linked journal entry, get the detailed line data
      if (transactionData.gl_journal_entry) {
        try {
          const journalResponse = await fetch(`http://localhost:8000/api/gl/journal-entries/${transactionData.gl_journal_entry}/`, {
            headers: {
              'Authorization': `Token ${token}`,
              'Content-Type': 'application/json',
            },
          });

          if (journalResponse.ok) {
            const journalData = await journalResponse.json();
            console.log('Loaded journal entry data:', journalData);
            
            // Parse journal lines to reconstruct payment lines (excluding the cash account line)
            const expenseLines = journalData.journal_lines?.filter((line: any) => 
              line.account_id !== transactionData.cash_account?.id && line.debit_amount > 0
            ) || [];

            if (expenseLines.length > 0) {
              paymentLines = expenseLines.map((line: any, index: number) => ({
                id: (index + 1).toString(),
                account_id: line.account_id,
                category_id: line.account_id,
                category_name: line.account_name || '',
                description: line.description || line.memo || '',
                amount: parseFloat(line.debit_amount) || 0,
                sales_tax: null,
                sales_tax_description: '',
                sales_tax_rate: 0,
                sales_tax_amount: 0,
                taxable_amount: parseFloat(line.debit_amount) || 0,
              }));
            }
          }
        } catch (journalError) {
          console.warn('Could not load journal entry details:', journalError);
          // Continue with basic transaction data
        }
      }
      
      // Set form data
      setFormData(prev => ({
        ...prev,
        payee: transactionData.vendor || transactionData.customer || transactionData.employee || '',
        payment_account_id: transactionData.cash_account?.id || null,
        payment_date: transactionData.transaction_date,
        ref_no: transactionData.reference_number || '',
        tags: [],
        memo: transactionData.memo || '',
        tds_rate_id: null,
        payment_lines: paymentLines,
        attachments: [],
      }));

      console.log('Form data set with', paymentLines.length, 'payment lines');
      
    } catch (error) {
      console.error('Error loading transaction:', error);
      setSnackbar({ 
        open: true, 
        message: 'Failed to load transaction data', 
        severity: 'error' 
      });
    } finally {
      setLoading(false);
    }
  };

  // Load data on mount
  useEffect(() => {
    const loadData = async () => {
      await Promise.all([
        loadPaymentAccounts(),
        loadGLAccounts(),
        loadTDSRates(),
        loadSalesTaxes(),
        loadEmployees(),
        loadCustomers(),
      ]);
      
      // Load transaction data after all other data is loaded
      if (id && (isEditMode || isViewMode)) {
        await loadTransactionData(id);
      }
    };
    
    loadData();
    
    // Reset table column widths to default when component mounts
    setResetTableColumns(true);
    setTimeout(() => setResetTableColumns(false), 100);
  }, [id]);

  const loadPaymentAccounts = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/cms/gl/cash-accounts/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Payment accounts response:', data);
        
        // Handle the response format - check if accounts are in data.accounts or data.results
        const accounts = data.accounts || data.results || [];
        
        // Transform to consistent format
        const formattedAccounts = accounts.map((account: any) => ({
          id: account.id,
          account_name: account.account_name,
          account_number: account.account_number,
          balance: account.current_balance || account.balance || 0,
          currency: account.currency || 'USD'
        }));
        
        setPaymentAccounts(formattedAccounts);
        console.log('Formatted payment accounts:', formattedAccounts);
      } else {
        console.error('Failed to load payment accounts:', response.statusText);
        console.error('Response:', await response.text());
      }
    } catch (error) {
      console.error('Error loading payment accounts:', error);
    }
  };

  const loadGLAccounts = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/cms/gl/expense-accounts/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('GL expense accounts response:', data);
        
        // Handle both paginated and direct response formats
        const accounts = data.results || data || [];
        
        // Transform to consistent format
        const formattedAccounts = accounts.map((account: any) => ({
          id: account.id,
          account_number: account.account_number,
          account_name: account.account_name,
          account_type: account.account_type_name || account.account_type || '',
          detail_type: account.detail_type_name || account.detail_type || ''
        }));
        
        setGLAccounts(formattedAccounts);
      } else {
        console.error('Failed to load GL accounts:', response.statusText);
      }
    } catch (error) {
      console.error('Error loading GL accounts:', error);
    }
  };

  const loadTDSRates = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/gl/tds/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('TDS rates response:', data);
        
        // Handle both paginated and direct response formats
        const rates = data.results || data || [];
        
        // Transform to consistent format
        const formattedRates = rates.map((rate: any) => ({
          id: rate.id,
          name: `${rate.description} (${rate.rate}%)`,
          percentage: rate.rate,
          description: rate.description,
          section: rate.section || '',
          tds_type: rate.tds_type || '',
          threshold_limit: rate.threshold_limit || 0
        }));
        
        setTdsRates(formattedRates);
        console.log('Formatted TDS rates:', formattedRates);
      } else {
        console.error('Failed to load TDS rates:', response.statusText);
        console.error('Response:', await response.text());
      }
    } catch (error) {
      console.error('Error loading TDS rates:', error);
      // Set default TDS rates if API fails
      setTdsRates([
        { id: 1, name: 'TDS 1% (1%)', percentage: 1, description: 'Standard TDS rate' },
        { id: 2, name: 'TDS 2% (2%)', percentage: 2, description: 'Higher TDS rate' },
        { id: 3, name: 'TDS 5% (5%)', percentage: 5, description: 'Professional services' },
        { id: 4, name: 'TDS 10% (10%)', percentage: 10, description: 'Contractor payments' },
      ]);
    }
  };

  const loadSalesTaxes = async () => {
    try {
      const salesTaxData = await salesTaxService.getAllSalesTaxes();
      console.log(`Cash Payment: Loaded ${salesTaxData.length} sales tax options`);
      setSalesTaxes(salesTaxData);
    } catch (error) {
      console.error('Error loading sales taxes:', error);
      setSnackbar({ 
        open: true, 
        message: 'Failed to load sales tax options', 
        severity: 'error' 
      });
    }
  };

  const loadEmployees = async () => {
    try {
      const employeeData = await EmployeeService.getActiveEmployees();
      console.log(`Cash Payment: Loaded ${employeeData.length} active employees`);
      setEmployees(employeeData);
    } catch (error) {
      console.error('Error loading employees:', error);
      setSnackbar({ 
        open: true, 
        message: 'Failed to load employee list', 
        severity: 'error' 
      });
    }
  };

  // Calculations
  const getBaseAmount = () => {
    return formData.payment_lines.reduce((sum, line) => sum + (line.amount || 0), 0);
  };

  const getTotalSalesTax = () => {
    return formData.payment_lines.reduce((sum, line) => sum + (line.sales_tax_amount || 0), 0);
  };

  const getTotalAmount = () => {
    return getBaseAmount() + getTotalSalesTax();
  };

  const getTDSAmount = () => {
    const selectedTDS = tdsRates.find(rate => rate.id === formData.tds_rate_id);
    if (selectedTDS) {
      return getTotalAmount() * (selectedTDS.percentage / 100);
    }
    return 0;
  };

  const getNetPayment = () => {
    return getTotalAmount() - getTDSAmount();
  };

  // Form handlers
  const handleAddLine = () => {
    const newLine: PaymentLine = {
      id: Date.now().toString(),
      account_id: null,
      category_id: null,
      category_name: '',
      description: '',
      amount: 0,
    };
    setFormData(prev => ({
      ...prev,
      payment_lines: [...prev.payment_lines, newLine],
    }));
  };

  const handleRemoveLine = (lineId: string) => {
    setFormData(prev => ({
      ...prev,
      payment_lines: prev.payment_lines.filter(line => line.id !== lineId),
    }));
  };

  const handleLineChange = (lineId: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      payment_lines: prev.payment_lines.map(line => {
        if (line.id === lineId) {
          const updatedLine = {
            ...line,
            // Handle new account_selection field from JournalLineTable
            ...(field === 'account_selection' && {
              account_id: value.account_id,
              category_id: value.account_id,
              category_name: value.account_name,
            }),
            // Handle other fields normally
            ...(field !== 'account_selection' && { [field]: value }),
            // Sync account_id and category_id for compatibility (legacy support)
            ...(field === 'account_id' && {
              category_id: value,
              category_name: glAccounts.find(acc => acc.id === value)?.account_name || '',
            }),
            ...(field === 'category_id' && {
              account_id: value,
              category_name: glAccounts.find(acc => acc.id === value)?.account_name || '',
            }),
          };

          // Calculate sales tax when sales tax is selected or amount changes
          if (field === 'sales_tax' || field === 'amount') {
                    const selectedSalesTax = field === 'sales_tax'
          ? salesTaxes.find(tax => Number(tax.id) === Number(value))
          : salesTaxes.find(tax => Number(tax.id) === Number(line.sales_tax));
            
            if (selectedSalesTax && updatedLine.amount > 0) {
              updatedLine.taxable_amount = updatedLine.amount;
              updatedLine.sales_tax_amount = (updatedLine.amount * selectedSalesTax.rate) / 100;
              updatedLine.sales_tax_description = selectedSalesTax.description;
              updatedLine.sales_tax_rate = selectedSalesTax.rate;
            } else {
              updatedLine.sales_tax_amount = 0;
              updatedLine.taxable_amount = 0;
              updatedLine.sales_tax_description = '';
              updatedLine.sales_tax_rate = 0;
            }
          }

          return updatedLine;
        }
        return line;
      }),
    }));
  };

  const formatCurrency = (amount: number, currencyCode?: string) => {
    const currency = currencyCode || currencyInfo?.functional_currency || 'USD';
    const locale = currency === 'INR' ? 'en-IN' : 'en-US';
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  };



  const handleSave = async () => {
    // ... existing validation code ...
    const validLines = formData.payment_lines.filter(line => line.category_id && line.amount > 0);

    if (!formData.payee) {
      setSnackbar({ open: true, message: '❌ Payee is required', severity: 'error' });
      return;
    }

    if (!formData.payment_account_id) {
      setSnackbar({ open: true, message: '❌ Payment account is required', severity: 'error' });
      return;
    }

    if (validLines.length === 0) {
      setSnackbar({ open: true, message: '❌ At least one valid expense line is required', severity: 'error' });
      return;
    }

    if (!formData.payment_date) {
      setSnackbar({ open: true, message: '❌ Payment date is required', severity: 'error' });
      return;
    }

    setLoading(true);
    console.log('=== USING NEW PAYMENT API ===');
    console.log('Sending base amounts + tax selections to backend for automatic calculation');
    
    try {
      const token = localStorage.getItem('token');
      console.log('Token:', token ? 'Present' : 'Missing');
      
      // Prepare payment data for new API
      const paymentData = {
        payee: formData.payee,
        payment_account_id: formData.payment_account_id,
        payment_date: formData.payment_date,
        reference_number: formData.ref_no || '',
        memo: formData.memo || '',
        tds_rate_id: formData.tds_rate_id,
        payment_lines: validLines.map(line => ({
          category_id: line.category_id,
          amount: line.amount, // Base amount only
          description: line.description || `Payment to ${formData.payee}`,
          memo: line.description || '',
          sales_tax_id: line.sales_tax,
          sales_tax_rate: line.sales_tax_rate
        }))
      };

      console.log('Payment data to send:', JSON.stringify(paymentData, null, 2));
      
      // Call appropriate API based on mode
      const url = isEditMode 
        ? `http://localhost:8000/api/cms/transactions/${id}/`
        : 'http://localhost:8000/api/cms/api/payments/create/';
      
      const method = isEditMode ? 'PUT' : 'POST';
      
      const requestData = isEditMode ? {
        transaction_date: formData.payment_date,
        description: validLines[0]?.description || `Payment to ${formData.payee}`,
        amount: validLines[0]?.amount || 0,
        reference_number: formData.ref_no,
        memo: formData.memo,
        customer: formData.payee.includes('@') ? formData.payee : '',
        vendor: !formData.payee.includes('@') ? formData.payee : '',
        cash_account_id: formData.payment_account_id,
      } : paymentData;

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });
      
      console.log('Response status:', response.status);

      if (response.ok) {
        const result = await response.json();
        console.log('Payment created successfully:', result);
        
        const calculations = result.calculations;
        
        setSnackbar({
          open: true,
          message: isEditMode 
            ? `✅ SUCCESS: Payment updated!`
            : `✅ SUCCESS: Payment created as DRAFT! Entry #${result.entry_number} - Ready for posting`,
          severity: 'success'
        });
        
        // Show detailed breakdown in alert
        alert(`✅ SUCCESS: Payment created as DRAFT!

Journal Entry: ${result.entry_number}
Status: ${result.journal_entry_status}

💰 CALCULATION BREAKDOWN:
Base Amount: ${formatCurrency(calculations.base_amount)}
Sales Tax: ${formatCurrency(calculations.sales_tax_amount)}
Total Amount: ${formatCurrency(calculations.total_amount)}
TDS Deduction: ${formatCurrency(calculations.tds_amount)}
Net Payment: ${formatCurrency(calculations.net_payment)}

📝 NEXT STEPS:
1. Review the payment details in the transactions page
2. Click "Post" when ready to finalize the payment
3. Once posted, the entry will appear in account ledgers

The payment is now saved as DRAFT and ready for review before posting.`);

        // Reset form only for new payments
        if (isCreateMode) {
          setFormData({
            payee: '',
            payment_account_id: null,
            payment_date: new Date().toISOString().slice(0, 10),
            ref_no: '',
            tags: [],
            memo: '',
            tds_rate_id: null,
            payment_lines: [
              {
                id: '1',
                account_id: null,
                category_id: null,
                category_name: '',
                amount: 0,
                description: '',
                sales_tax: null,
                sales_tax_description: '',
                sales_tax_rate: 0,
                sales_tax_amount: 0,
                taxable_amount: 0
              }
            ],
            attachments: []
          });
        }

      } else {
        const errorData = await response.text();
        console.error('Failed to create payment. Status:', response.status);
        console.error('Response body:', errorData);
        
        let parsedError;
        try {
          parsedError = JSON.parse(errorData);
        } catch {
          parsedError = { error: errorData };
        }
        
        const errorMessage = parsedError.error || parsedError.message || errorData;
        setSnackbar({
          open: true,
          message: `❌ ERROR: ${errorMessage}`,
          severity: 'error'
        });
        
        alert(`❌ ERROR: Failed to save payment\n\nDetails: ${errorMessage}`);
      }
      
    } catch (error) {
      console.error('Error creating payment:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setSnackbar({
        open: true,
        message: `❌ ERROR: ${errorMessage}`,
        severity: 'error'
      });
      
      alert(`❌ ERROR: Failed to save payment\n\nError: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveAndClose = async () => {
    await handleSave();
    if (!error) {
      navigate('/dashboard/cms/transactions');
    }
  };

  const handleCancel = () => {
    navigate('/dashboard/cms/transactions');
  };

  if (currencyLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <Typography>Loading...</Typography>
      </Box>
    );
  }

  return (
    <>
      {/* Full Screen Layout */}
      <Box
        sx={{
          position: 'fixed',
          top: '64px', // Account for navigation bar height
          left: 0,
          right: 0,
          bottom: 0,
          bgcolor: 'background.default',
          zIndex: 1200,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {/* Header */}
        <Box
          sx={{
            bgcolor: 'background.paper',
            borderBottom: 1,
            borderColor: 'divider',
            p: 2,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold" color="primary">
              {isViewMode ? 'VIEW CASH PAYMENT' : isEditMode ? 'EDIT CASH PAYMENT' : 'CASH PAYMENT FORM'}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {isViewMode ? 'View payment details' : isEditMode ? 'Modify cash payment' : 'Record cash payments and expenses efficiently'}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
            {/* Amount Display */}
            <Box sx={{ textAlign: 'right', minWidth: '200px' }}>
              <CurrencyDisplay 
                value={getNetPayment()}
                currencyCode={currencyInfo?.functional_currency}
                variant="h4"
                color="primary"
                sx={{ fontWeight: 'bold' }}
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Net Payment Amount
              </Typography>
            </Box>
            <IconButton 
              onClick={handleCancel} 
              sx={{ 
                color: 'text.primary',
                '&:hover': {
                  backgroundColor: 'action.hover',
                  color: 'error.main'
                }
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Content */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
          <Grid container spacing={3}>
            {/* Main Form - Full Width */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Grid container spacing={3}>
                    {/* Basic Information */}
                    <Grid item xs={12} md={4}>
                      <Autocomplete
                        fullWidth
                        freeSolo
                        disabled={isViewMode}
                        options={[
                          ...employees.map(emp => ({ ...emp, type: 'employee' })),
                          ...customers.map(cust => ({ ...cust, type: 'customer' }))
                        ]}
                        groupBy={(option) => option.type === 'employee' ? 'Employees' : 'Customers'}
                        getOptionLabel={(option) => {
                          if (typeof option === 'string') {
                            return option;
                          }
                          return option.type === 'employee' ? (option as any).full_name : (option as any).fullName;
                        }}
                        value={formData.payee}
                        onChange={(event, newValue) => {
                          if (typeof newValue === 'string') {
                            setFormData(prev => ({ ...prev, payee: newValue }));
                          } else if (newValue) {
                            const name = newValue.type === 'employee' ? (newValue as any).full_name : (newValue as any).fullName;
                            setFormData(prev => ({ ...prev, payee: name }));
                          } else {
                            setFormData(prev => ({ ...prev, payee: '' }));
                          }
                        }}
                        onInputChange={(event, newInputValue) => {
                          setFormData(prev => ({ ...prev, payee: newInputValue }));
                        }}
                        renderInput={(params) => (
                          <TextField 
                            {...params} 
                            label="Payee"
                            placeholder="Who did you pay? (Type or select from employees/customers)"
                            required
                            disabled={isViewMode}
                          />
                        )}
                        renderOption={(props, option) => (
                          <Box component="li" {...props}>
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {option.type === 'employee' ? (option as any).full_name : (option as any).fullName}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {option.type === 'employee' 
                                  ? `${'position' in option ? option.position : ''} - ${'department' in option ? option.department : ''}`
                                  : `${'customerType' in option ? option.customerType : ''} - ${option.email || 'No email'}`
                                }
                              </Typography>
                            </Box>
                          </Box>
                        )}
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <StandardDatePicker
                        label="Payment Date"
                        value={dayjs(formData.payment_date)}
                        onChange={(date) => setFormData(prev => ({ ...prev, payment_date: date?.format('YYYY-MM-DD') || '' }))}
                        required
                        disabled={isViewMode}
                        businessContext="payment"
                        showQuickActions
                        dateFormat="DD/MM/YYYY"
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <FormControl fullWidth required>
                        <InputLabel>Payment Account</InputLabel>
                        <Select
                          value={formData.payment_account_id || ''}
                          onChange={(e) => setFormData(prev => ({ ...prev, payment_account_id: Number(e.target.value) }))}
                          label="Payment Account"
                          disabled={isViewMode}
                        >
                          {paymentAccounts.length === 0 ? (
                            <MenuItem disabled>
                              <em>No cash accounts found</em>
                            </MenuItem>
                          ) : (
                            paymentAccounts.map((account) => (
                              <MenuItem key={account.id} value={account.id}>
                                <Box>
                                  <Typography variant="body1" sx={{ fontWeight: 600, fontSize: '1rem' }}>
                                    {account.account_number} - {account.account_name}
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                                    Balance: {formatCurrency(account.balance, account.currency)}
                                  </Typography>
                                </Box>
                              </MenuItem>
                            ))
                          )}
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} md={2}>
                      <TextField
                        fullWidth
                        label="Ref No."
                        value={formData.ref_no}
                        onChange={(e) => setFormData(prev => ({ ...prev, ref_no: e.target.value }))}
                        disabled={isViewMode}
                      />
                    </Grid>

                    {/* Tags */}
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Tags"
                        multiline
                        rows={2}
                        value={formData.tags.join(', ')}
                        onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value.split(',').map(tag => tag.trim()) }))}
                        placeholder="Separate with commas"
                      />
                    </Grid>

                    {/* Amount Summary - Integrated */}
                    <Grid item xs={12} md={6}>
                      <Card variant="outlined" sx={{ bgcolor: '#f8f9fa', p: 2 }}>
                        <Typography variant="h6" gutterBottom color="primary">
                          Amount Summary
                        </Typography>
                        
                        <Box sx={{ mb: 2 }}>
                          {/* Base Amount */}
                          <Box display="flex" justifyContent="space-between" mb={1}>
                            <Typography color="text.secondary">Base Amount:</Typography>
                            <CurrencyDisplay 
                              value={getBaseAmount()}
                              currencyCode={currencyInfo?.functional_currency}
                              color="text.secondary"
                            />
                          </Box>

                          {/* Sales Tax (if any) */}
                          {getTotalSalesTax() > 0 && (
                            <Box display="flex" justifyContent="space-between" mb={1}>
                              <Typography color="text.secondary">Sales Tax:</Typography>
                              <CurrencyDisplay 
                                value={getTotalSalesTax()}
                                currencyCode={currencyInfo?.functional_currency}
                                color="info.main"
                                sx={{ fontWeight: 'bold' }}
                              />
                            </Box>
                          )}

                          <Divider sx={{ my: 1 }} />

                          {/* Total Amount (Base + Sales Tax) */}
                          <Box display="flex" justifyContent="space-between" mb={1}>
                            <Typography variant="h6" fontWeight="bold">Total Amount:</Typography>
                            <CurrencyDisplay 
                              value={getTotalAmount()}
                              currencyCode={currencyInfo?.functional_currency}
                              variant="h6"
                              color="primary"
                              sx={{ fontWeight: 'bold' }}
                            />
                          </Box>

                          {/* TDS Section */}
                          <FormControl fullWidth size="small" sx={{ mb: 1 }}>
                            <InputLabel>TDS Rate</InputLabel>
                            <Select
                              value={formData.tds_rate_id || ''}
                              onChange={(e) => setFormData(prev => ({ ...prev, tds_rate_id: Number(e.target.value) || null }))}
                              label="TDS Rate"
                            >
                              <MenuItem value="">
                                <em>No TDS</em>
                              </MenuItem>
                              {tdsRates.map((rate) => (
                                <MenuItem key={rate.id} value={rate.id}>
                                  <Box>
                                    <Typography variant="body1" sx={{ fontWeight: 600, fontSize: '1rem' }}>
                                      {rate.description} - {rate.percentage}%
                                    </Typography>
                                    {rate.section && (
                                      <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                                        Section: {rate.section}
                                      </Typography>
                                    )}
                                  </Box>
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>

                          {formData.tds_rate_id && (
                            <Box display="flex" justifyContent="space-between" mb={1}>
                              <Typography color="text.secondary">
                                TDS ({tdsRates.find(r => r.id === formData.tds_rate_id)?.percentage}%):
                              </Typography>
                              <CurrencyDisplay 
                                value={-getTDSAmount()}
                                currencyCode={currencyInfo?.functional_currency}
                                color="error.main"
                                sx={{ fontWeight: 'bold' }}
                              />
                            </Box>
                          )}

                          <Divider sx={{ my: 1 }} />
                          
                          <Box display="flex" justifyContent="space-between">
                            <Typography variant="h5" fontWeight="bold">Net Amount:</Typography>
                            <CurrencyDisplay 
                              value={getNetPayment()}
                              currencyCode={currencyInfo?.functional_currency}
                              variant="h5"
                              color="primary"
                              sx={{ fontWeight: 'bold' }}
                            />
                          </Box>
                        </Box>
                      </Card>
                    </Grid>

                    {/* Line Items */}
                    <Grid item xs={12}>
                      <Typography variant="h6" gutterBottom sx={{ mb: 1 }}>
                        Category Details
                      </Typography>
                      <JournalLineTable
                        tableMode="payment"
                        lines={formData.payment_lines}
                        accounts={glAccounts}
                        salesTaxes={salesTaxes}
                        onLineChange={handleLineChange}
                        onAddLine={handleAddLine}
                        onRemoveLine={handleRemoveLine}
                        currencySymbol={currencyInfo?.functional_currency_symbol || '$'}
                        accountColumnLabel="Category"
                        accountPlaceholder="Select Category"
                        descriptionPlaceholder="Enter description"
                        salesTaxPlaceholder="Select Sales Tax"
                        minLines={4}
                        resetColumnWidths={resetTableColumns}
                        tableHeight="300px"
                        readOnly={isViewMode}
                      />
                    </Grid>

                    {/* Memo */}
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Memo"
                        multiline
                        rows={3}
                        value={formData.memo}
                        onChange={(e) => setFormData(prev => ({ ...prev, memo: e.target.value }))}
                        placeholder="Add any additional notes..."
                        disabled={isViewMode}
                      />
                    </Grid>

                    {/* Attachments */}
                    <Grid item xs={12}>
                      <Button
                        startIcon={<AttachFileIcon />}
                        variant="outlined"
                        component="label"
                      >
                        Attach Files
                        <input type="file" hidden multiple />
                      </Button>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>


          </Grid>
        </Box>

        {/* Footer Actions */}
        <Box
          sx={{
            bgcolor: 'background.paper',
            borderTop: 1,
            borderColor: 'divider',
            p: 2,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Button
            startIcon={<CancelIcon />}
            onClick={handleCancel}
            color="inherit"
          >
            Cancel
          </Button>

          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              startIcon={<PrintIcon />}
              variant="outlined"
              color="inherit"
            >
              Print
            </Button>
            {!isViewMode && (
              <Button
                startIcon={<RepeatIcon />}
                variant="outlined"
                color="inherit"
              >
                Make Recurring
              </Button>
            )}
            {!isViewMode && (
              <Button
                startIcon={<SaveIcon />}
                variant="outlined"
                onClick={handleSave}
                disabled={loading}
              >
                {isEditMode ? 'Update' : 'Save'}
              </Button>
            )}
            {!isViewMode && (
              <Button
                startIcon={<SaveAltIcon />}
                variant="contained"
                onClick={handleSaveAndClose}
                disabled={loading}
              >
                {isEditMode ? 'Update and Close' : 'Save and Close'}
              </Button>
            )}
          </Box>
        </Box>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={10000}
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
          anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        >
          <Alert 
            onClose={() => setSnackbar(prev => ({ ...prev, open: false }))} 
            severity={snackbar.severity}
            sx={{ 
              fontSize: '1rem',
              fontWeight: 'bold',
              minWidth: '400px'
            }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </>
  );
};

export default CashPaymentForm; 