// API Configuration
const API_BASE_URL = 'http://localhost:8000';

export interface Vendor {
  id: number;
  vendor_id: string;
  display_name: string;
  vendor_type: 'individual' | 'business' | 'contractor' | 'supplier';
  company_name?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  fax?: string;
  website?: string;
  billing_street?: string;
  billing_city?: string;
  billing_state?: string;
  billing_postal_code?: string;
  billing_country: string;
  shipping_street?: string;
  shipping_city?: string;
  shipping_state?: string;
  shipping_postal_code?: string;
  shipping_country?: string;
  tax_registration_number?: string;
  pan_number?: string;
  gstin?: string;
  opening_balance: number;
  opening_balance_date?: string;
  current_balance: number;
  credit_limit?: number;
  payment_terms: number;
  currency: string;
  vendor_category?: string;
  lead_time_days: number;
  minimum_order_amount: number;
  status: 'active' | 'inactive';
  taxable: boolean;
  tax_exempt_reason?: string;
  preferred_vendor: boolean;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface VendorStats {
  total_vendors: number;
  active_vendors: number;
  inactive_vendors: number;
  preferred_vendors: number;
  total_payables: number;
  new_vendors_30_days: number;
}

export interface VendorFormData {
  firstName: string;
  lastName: string;
  displayName: string;
  companyName: string;
  email: string;
  phone: string;
  mobile: string;
  billingAddress: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  shippingAddress: {
    sameAsBilling: boolean;
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  paymentTerms: string;
  creditLimit: number;
  vendorCategory: string;
  leadTimeDays: number;
  minimumOrderAmount: number;
  preferredVendor: boolean;
  notes: string;
}

class VendorService {
  private baseUrl = `${API_BASE_URL}/api/contacts/vendors`;

  async getVendors(params?: {
    page?: number;
    search?: string;
    status?: string;
    vendor_type?: string;
    preferred_vendor?: boolean;
  }) {
    const token = localStorage.getItem('token');
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.vendor_type) queryParams.append('vendor_type', params.vendor_type);
    if (params?.preferred_vendor !== undefined) queryParams.append('preferred_vendor', params.preferred_vendor.toString());

    const response = await fetch(`${this.baseUrl}/?${queryParams}`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch vendors');
    }

    return response.json();
  }

  async getAllVendors(params?: {
    search?: string;
    status?: string;
    vendor_type?: string;
    preferred_vendor?: boolean;
  }): Promise<Vendor[]> {
    const token = localStorage.getItem('token');
    
    // Get all vendors by fetching all pages
    let allVendors: Vendor[] = [];
    let currentPage = 1;
    let hasMore = true;
    
    while (hasMore) {
      const queryParams = new URLSearchParams();
      queryParams.append('page', currentPage.toString());
      
      if (params?.search) queryParams.append('search', params.search);
      if (params?.status) queryParams.append('status', params.status);
      if (params?.vendor_type) queryParams.append('vendor_type', params.vendor_type);
      if (params?.preferred_vendor !== undefined) queryParams.append('preferred_vendor', params.preferred_vendor.toString());

      const response = await fetch(`${this.baseUrl}/?${queryParams}`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch vendors');
      }

      const data = await response.json();
      
      if (data && typeof data === 'object' && 'results' in data) {
        allVendors = [...allVendors, ...(data.results || [])];
        hasMore = data.next !== null;
        currentPage++;
      } else if (Array.isArray(data)) {
        allVendors = data;
        hasMore = false;
      } else {
        hasMore = false;
      }
    }
    
    return allVendors;
  }

  async getVendorsForDropdown(): Promise<Vendor[]> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/dropdown/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch vendors for dropdown');
    }

    return response.json();
  }

  async getVendorStats(): Promise<VendorStats> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/stats/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch vendor stats');
    }

    return response.json();
  }

  async getVendor(id: number): Promise<Vendor> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/${id}/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch vendor');
    }

    return response.json();
  }

  async createVendor(vendorData: VendorFormData): Promise<Vendor> {
    const token = localStorage.getItem('token');
    
    // Transform form data to API format
    // Transform form data to API format
const apiData = {
  display_name: vendorData.displayName,
  vendor_type: vendorData.companyName ? 'business' : 'individual',
  // Updated company_name handling (choose one option):
  // Option 1: If backend expects string (with empty string fallback)
  company_name: vendorData.companyName || '',
  // OR Option 2: If backend expects array
  // company_name: vendorData.companyName ? [vendorData.companyName] : [],
  
  // Personal information
  first_name: vendorData.firstName || '',
  last_name: vendorData.lastName || '',
  email: vendorData.email || '',
  phone: vendorData.phone || '',
  mobile: vendorData.mobile || '',

  // Billing address - using empty strings instead of null
  billing_street: vendorData.billingAddress.street || '',
  billing_city: vendorData.billingAddress.city || '',
  billing_state: vendorData.billingAddress.state || '',
  billing_postal_code: vendorData.billingAddress.postalCode || '',
  billing_country: vendorData.billingAddress.country || 'India',

  // Shipping address - using empty strings instead of null
  shipping_street: vendorData.shippingAddress.sameAsBilling 
    ? vendorData.billingAddress.street 
    : vendorData.shippingAddress.street || '',
  shipping_city: vendorData.shippingAddress.sameAsBilling 
    ? vendorData.billingAddress.city 
    : vendorData.shippingAddress.city || '',
  shipping_state: vendorData.shippingAddress.sameAsBilling 
    ? vendorData.billingAddress.state 
    : vendorData.shippingAddress.state || '',
  shipping_postal_code: vendorData.shippingAddress.sameAsBilling 
    ? vendorData.billingAddress.postalCode 
    : vendorData.shippingAddress.postalCode || '',
  shipping_country: vendorData.shippingAddress.sameAsBilling 
    ? vendorData.billingAddress.country 
    : vendorData.shippingAddress.country || '',

  // Financial information
  payment_terms: parseInt(vendorData.paymentTerms) || 0,  // Added fallback for parseInt
  credit_limit: vendorData.creditLimit || 0,  // Using 0 instead of null for numbers
  vendor_category: vendorData.vendorCategory || '',
  
  // Vendor settings
  lead_time_days: vendorData.leadTimeDays || 0,
  minimum_order_amount: vendorData.minimumOrderAmount || 0,
  preferred_vendor: vendorData.preferredVendor || false,
  notes: vendorData.notes || '',
  
  // Added status field if required by backend
  status: 'active'
};

    const response = await fetch(this.baseUrl + '/', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to create vendor');
    }

    return response.json();
  }

  async updateVendor(id: number, vendorData: VendorFormData): Promise<Vendor> {
    const token = localStorage.getItem('token');
    
    // Transform form data to API format
    const apiData = {
      display_name: vendorData.displayName,
      vendor_type: vendorData.companyName ? 'business' : 'individual',
      company_name: vendorData.companyName || null,
      first_name: vendorData.firstName || null,
      last_name: vendorData.lastName || null,
      email: vendorData.email || null,
      phone: vendorData.phone || null,
      mobile: vendorData.mobile || null,
      billing_street: vendorData.billingAddress.street || null,
      billing_city: vendorData.billingAddress.city || null,
      billing_state: vendorData.billingAddress.state || null,
      billing_postal_code: vendorData.billingAddress.postalCode || null,
      billing_country: vendorData.billingAddress.country || 'India',
      shipping_street: vendorData.shippingAddress.sameAsBilling ? vendorData.billingAddress.street : vendorData.shippingAddress.street || null,
      shipping_city: vendorData.shippingAddress.sameAsBilling ? vendorData.billingAddress.city : vendorData.shippingAddress.city || null,
      shipping_state: vendorData.shippingAddress.sameAsBilling ? vendorData.billingAddress.state : vendorData.shippingAddress.state || null,
      shipping_postal_code: vendorData.shippingAddress.sameAsBilling ? vendorData.billingAddress.postalCode : vendorData.shippingAddress.postalCode || null,
      shipping_country: vendorData.shippingAddress.sameAsBilling ? vendorData.billingAddress.country : vendorData.shippingAddress.country || null,
      payment_terms: parseInt(vendorData.paymentTerms),
      credit_limit: vendorData.creditLimit || null,
      vendor_category: vendorData.vendorCategory || null,
      lead_time_days: vendorData.leadTimeDays || 0,
      minimum_order_amount: vendorData.minimumOrderAmount || 0,
      preferred_vendor: vendorData.preferredVendor || false,
      notes: vendorData.notes || null,
    };

    const response = await fetch(`${this.baseUrl}/${id}/`, {
      method: 'PUT',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to update vendor');
    }

    return response.json();
  }

  async deleteVendor(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/${id}/`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Token ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to delete vendor');
    }
  }
}

export const vendorService = new VendorService();