// API Configuration
const API_BASE_URL = 'http://localhost:8000';

export interface Vendor {
  id: number;
  vendor_id: string;
  display_name: string;
  vendor_type: 'individual' | 'business' | 'contractor' | 'supplier';
  company_name?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  fax?: string;
  website?: string;
  billing_street?: string;
  billing_city?: string;
  billing_state?: string;
  billing_postal_code?: string;
  billing_country: string;
  shipping_street?: string;
  shipping_city?: string;
  shipping_state?: string;
  shipping_postal_code?: string;
  shipping_country?: string;
  tax_registration_number?: string;
  pan_number?: string;
  gstin?: string;
  opening_balance: number;
  opening_balance_date?: string;
  current_balance: number;
  credit_limit?: number;
  payment_terms: number;
  currency: string;
  vendor_category?: string;
  lead_time_days: number;
  minimum_order_amount: number;
  status: 'active' | 'inactive';
  taxable: boolean;
  tax_exempt_reason?: string;
  preferred_vendor: boolean;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface VendorStats {
  total_vendors: number;
  active_vendors: number;
  inactive_vendors: number;
  preferred_vendors: number;
  total_payables: number;
  new_vendors_30_days: number;
}

export interface VendorFormData {
  firstName: string;
  lastName: string;
  displayName: string;
  companyName: string;
  email: string;
  phone: string;
  mobile: string;
  billingAddress: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  shippingAddress: {
    sameAsBilling: boolean;
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  paymentTerms: string;
  creditLimit: number;
  vendorCategory: string;
  leadTimeDays: number;
  minimumOrderAmount: number;
  preferredVendor: boolean;
  notes: string;
}

class VendorService {
  private baseUrl = `${API_BASE_URL}/api/contacts/vendors`;
  private purchaseBaseUrl = `${API_BASE_URL}/api/purchase/vendors`;

  async getVendors(params?: {
    page?: number;
    search?: string;
    status?: string;
    vendor_type?: string;
    preferred_vendor?: boolean;
  }) {
    const token = localStorage.getItem('token');
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.vendor_type) queryParams.append('vendor_type', params.vendor_type);
    if (params?.preferred_vendor !== undefined) queryParams.append('preferred_vendor', params.preferred_vendor.toString());

    const response = await fetch(`${this.baseUrl}/?${queryParams}`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch vendors');
    }

    return response.json();
  }

  async getAllVendors(params?: {
    search?: string;
    status?: string;
    vendor_type?: string;
    preferred_vendor?: boolean;
  }): Promise<Vendor[]> {
    const token = localStorage.getItem('token');
    
    // Get all vendors by fetching all pages
    let allVendors: Vendor[] = [];
    let currentPage = 1;
    let hasMore = true;
    
    while (hasMore) {
      const queryParams = new URLSearchParams();
      queryParams.append('page', currentPage.toString());
      
      if (params?.search) queryParams.append('search', params.search);
      if (params?.status) queryParams.append('status', params.status);
      if (params?.vendor_type) queryParams.append('vendor_type', params.vendor_type);
      if (params?.preferred_vendor !== undefined) queryParams.append('preferred_vendor', params.preferred_vendor.toString());

      const response = await fetch(`${this.baseUrl}/?${queryParams}`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch vendors');
      }

      const data = await response.json();
      
      if (data && typeof data === 'object' && 'results' in data) {
        allVendors = [...allVendors, ...(data.results || [])];
        hasMore = data.next !== null;
        currentPage++;
      } else if (Array.isArray(data)) {
        allVendors = data;
        hasMore = false;
      } else {
        hasMore = false;
      }
    }
    
    return allVendors;
  }

  async getVendorsForDropdown(): Promise<Vendor[]> {
    const token = localStorage.getItem('token');

    if (!token) {
      throw new Error('No authentication token found');
    }

    // Try contacts endpoint first, then purchase endpoint as fallback
    const endpoints = [
      { url: `${this.baseUrl}/`, name: 'contacts' },
      { url: `${this.purchaseBaseUrl}/`, name: 'purchase' }
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`Trying ${endpoint.name} vendors endpoint:`, endpoint.url);

        const response = await fetch(endpoint.url, {
          headers: {
            'Authorization': `Token ${token}`,
            'Content-Type': 'application/json',
          },
        });

        console.log(`${endpoint.name} API response status:`, response.status);

        if (response.ok) {
          const data = await response.json();
          console.log(`Raw ${endpoint.name} API response:`, data);

          // Handle both paginated and direct array responses
          if (data && typeof data === 'object' && 'results' in data) {
            console.log(`Found paginated response with ${data.results?.length || 0} vendors from ${endpoint.name}`);
            return data.results || [];
          } else if (Array.isArray(data)) {
            console.log(`Found direct array response with ${data.length} vendors from ${endpoint.name}`);
            return data;
          } else {
            console.log(`Unexpected response format from ${endpoint.name}:`, typeof data);
          }
        } else {
          const errorText = await response.text();
          console.error(`${endpoint.name} API error:`, response.status, errorText);
        }
      } catch (error) {
        console.error(`Error with ${endpoint.name} endpoint:`, error);
      }
    }

    throw new Error('Failed to fetch vendors from all available endpoints');
  }

  async getVendorStats(): Promise<VendorStats> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/stats/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch vendor stats');
    }

    return response.json();
  }

  async getVendor(id: number): Promise<Vendor> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/${id}/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch vendor');
    }

    return response.json();
  }

  async createVendor(vendorData: VendorFormData): Promise<Vendor> {
    const token = localStorage.getItem('token');

    // Transform form data to match backend serializer field names (camelCase)
    const apiData = {
      displayName: vendorData.displayName,
      firstName: vendorData.firstName,
      lastName: vendorData.lastName,
      companyName: vendorData.companyName,
      email: vendorData.email,
      phone: vendorData.phone,
      mobile: vendorData.mobile,
      billingAddress: vendorData.billingAddress,
      shippingAddress: vendorData.shippingAddress,
      paymentTerms: vendorData.paymentTerms,
      creditLimit: vendorData.creditLimit,
      vendorCategory: vendorData.vendorCategory,
      leadTimeDays: vendorData.leadTimeDays,
      minimumOrderAmount: vendorData.minimumOrderAmount,
      preferredVendor: vendorData.preferredVendor,
      notes: vendorData.notes,
      is_active: true
    };

    const response = await fetch(this.baseUrl + '/', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to create vendor');
    }

    return response.json();
  }

  async updateVendor(id: number, vendorData: VendorFormData): Promise<Vendor> {
    const token = localStorage.getItem('token');

    // Transform form data to match backend serializer field names (camelCase)
    const apiData = {
      displayName: vendorData.displayName,
      firstName: vendorData.firstName,
      lastName: vendorData.lastName,
      companyName: vendorData.companyName,
      email: vendorData.email,
      phone: vendorData.phone,
      mobile: vendorData.mobile,
      billingAddress: vendorData.billingAddress,
      shippingAddress: vendorData.shippingAddress,
      paymentTerms: vendorData.paymentTerms,
      creditLimit: vendorData.creditLimit,
      vendorCategory: vendorData.vendorCategory,
      leadTimeDays: vendorData.leadTimeDays,
      minimumOrderAmount: vendorData.minimumOrderAmount,
      preferredVendor: vendorData.preferredVendor,
      notes: vendorData.notes,
      is_active: true
    };

    const response = await fetch(`${this.baseUrl}/${id}/`, {
      method: 'PUT',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to update vendor');
    }

    return response.json();
  }

  async deleteVendor(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/${id}/`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Token ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to delete vendor');
    }
  }
}

export const vendorService = new VendorService();