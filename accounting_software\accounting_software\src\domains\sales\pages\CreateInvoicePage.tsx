import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Button,
  Paper,
  Typography,
  IconButton,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Divider,
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  InputAdornment,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Close as CloseIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { invoiceService, Invoice, InvoiceItem, InvoiceCustomer } from '../../../services/invoice.service';
import { formatCurrency } from '../../../shared/utils/formatters';
import FormattedCurrencyInput from '../../../shared/components/FormattedCurrencyInput';
import { customerService } from '../../../services/customer.service';
import ConfirmationDialog from '../../../shared/components/ConfirmationDialog';
import { useConfirmation } from '../../../shared/hooks/useConfirmation';

const CreateInvoicePage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditing = Boolean(id);
  const { confirmation, showSuccess, showError, hideConfirmation } = useConfirmation();

  // State
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<InvoiceCustomer[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  
  // Form data
  const [formData, setFormData] = useState<Partial<Invoice>>({
    customer: 0,
    invoice_date: dayjs().format('YYYY-MM-DD'),
    due_date: dayjs().add(30, 'days').format('YYYY-MM-DD'),
    status: 'draft',
    items: [],
    subtotal: 0,
    tax_total: 0,
    total_amount: 0,
    notes: '',
    terms: 'Payment due within 30 days',
  });

  // Load data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // For now, use mock data to avoid API errors until backend is ready
        setCustomers([
          { id: 1, display_name: 'Acme Corp', email: '<EMAIL>', payment_terms: 'net_30' },
          { id: 2, display_name: 'Tech Solutions Ltd', email: '<EMAIL>', payment_terms: 'net_15' },
          { id: 3, display_name: 'Global Industries', email: '<EMAIL>', payment_terms: 'net_45' },
        ]);
        
        setProducts([
          { id: 1, name: 'Consulting Services', sales_price: 150, description: 'Professional consulting' },
          { id: 2, name: 'Software License', sales_price: 500, description: 'Annual software license' },
          { id: 3, name: 'Training Program', sales_price: 300, description: 'Staff training program' },
        ]);

        // If editing, load the invoice
        if (isEditing && id) {
          const invoice = await invoiceService.getInvoice(parseInt(id));
          setFormData(invoice);
        }
        
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [id, isEditing]);

  // Handlers
  const handleFieldChange = (field: keyof Invoice, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Auto-calculate due date when customer changes
    if (field === 'customer' && value) {
      const customer = customers.find(c => c.id === value);
      if (customer?.payment_terms && formData.invoice_date) {
        const dueDate = invoiceService.calculateDueDate(formData.invoice_date, customer.payment_terms);
        setFormData(prev => ({ ...prev, due_date: dueDate }));
      }
    }
  };

  const addLineItem = () => {
    const newItem: InvoiceItem = {
      description: '',
      quantity: 1,
      unit_price: 0,
      line_total: 0,
      tax_rate: 0,
      tax_amount: 0,
    };
    
    setFormData(prev => ({
      ...prev,
      items: [...(prev.items || []), newItem]
    }));
  };

  const updateLineItem = (index: number, field: keyof InvoiceItem, value: any) => {
    setFormData(prev => {
      const items = [...(prev.items || [])];
      items[index] = { ...items[index], [field]: value };
      
      // Auto-calculate line total
      if (field === 'quantity' || field === 'unit_price') {
        const quantity = field === 'quantity' ? value : items[index].quantity;
        const unitPrice = field === 'unit_price' ? value : items[index].unit_price;
        items[index].line_total = quantity * unitPrice;
        
        // Calculate tax
        const taxRate = items[index].tax_rate || 0;
        items[index].tax_amount = (items[index].line_total * taxRate) / 100;
      }
      
      // Recalculate totals
      const totals = invoiceService.calculateTotals(items);
      
      return {
        ...prev,
        items,
        ...totals
      };
    });
  };

  const removeLineItem = (index: number) => {
    setFormData(prev => {
      const items = (prev.items || []).filter((_, i) => i !== index);
      const totals = invoiceService.calculateTotals(items);
      
      return {
        ...prev,
        items,
        ...totals
      };
    });
  };

  const handleProductSelect = (index: number, product: any) => {
    if (product) {
      updateLineItem(index, 'product', product.id);
      updateLineItem(index, 'product_name', product.name);
      updateLineItem(index, 'description', product.description || product.name);
      updateLineItem(index, 'unit_price', product.sales_price || 0);
    }
  };

  const handleSave = async (saveAndClose = false) => {
    try {
      setLoading(true);
      setError(null);

      // Validation
      if (!formData.customer) {
        throw new Error('Please select a customer');
      }
      if (!formData.items?.length) {
        throw new Error('Please add at least one line item');
      }

      const invoiceData = {
        customer: formData.customer!,
        invoice_date: formData.invoice_date!,
        due_date: formData.due_date!,
        status: formData.status!,
        items: formData.items!,
        subtotal: formData.subtotal || 0,
        tax_total: formData.tax_total || 0,
        total_amount: formData.total_amount || 0,
        notes: formData.notes || '',
        terms: formData.terms || '',
      };

      let savedInvoice: Invoice;
      if (isEditing && id) {
        savedInvoice = await invoiceService.updateInvoice(parseInt(id), invoiceData);
        showSuccess(
          'Invoice Updated!',
          `Invoice has been updated successfully.`
        );
      } else {
        savedInvoice = await invoiceService.createInvoice(invoiceData);
        showSuccess(
          'Invoice Created!',
          `Invoice has been created successfully.`
        );
      }

      if (saveAndClose) {
        setTimeout(() => {
          navigate('/dashboard/sales/invoices');
        }, 1500);
      } else {
        // Stay on page but show success message
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save invoice';
      showError(
        'Save Failed',
        errorMessage
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard/sales/invoices');
  };

  const selectedCustomer = customers.find(c => c.id === formData.customer);

  return (
    <Box sx={{ 
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 1300,
      display: 'flex', 
      flexDirection: 'column',
      backgroundColor: 'white',
    }}>
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        p: 2,
        borderBottom: 1,
        borderColor: 'divider'
      }}>
        <Typography variant="h5">
          {isEditing ? 'Edit Invoice' : 'Create New Invoice'}
        </Typography>
        <IconButton onClick={handleCancel}>
          <CloseIcon />
        </IconButton>
      </Box>
      
      {/* Main Content */}
      <Box sx={{ 
        flexGrow: 1, 
        overflow: 'auto', 
        p: 3,
        backgroundColor: '#f5f5f5'
      }}>


        {/* Invoice Details */}
        <Card sx={{ mb: 3 }}>
          <CardHeader title="Invoice Details" />
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Autocomplete
                  options={customers}
                  getOptionLabel={(option) => option.display_name}
                  value={selectedCustomer || null}
                  onChange={(_, value) => handleFieldChange('customer', value?.id || 0)}
                  renderInput={(params) => (
                    <TextField {...params} label="Customer" required fullWidth />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="Invoice Date"
                  value={dayjs(formData.invoice_date)}
                  onChange={(date) => handleFieldChange('invoice_date', date?.format('YYYY-MM-DD'))}
                  slotProps={{ textField: { fullWidth: true, required: true } }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="Due Date"
                  value={dayjs(formData.due_date)}
                  onChange={(date) => handleFieldChange('due_date', date?.format('YYYY-MM-DD'))}
                  slotProps={{ textField: { fullWidth: true, required: true } }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={formData.status}
                    onChange={(e) => handleFieldChange('status', e.target.value)}
                    label="Status"
                  >
                    <MenuItem value="draft">Draft</MenuItem>
                    <MenuItem value="sent">Sent</MenuItem>
                    <MenuItem value="paid">Paid</MenuItem>
                    <MenuItem value="overdue">Overdue</MenuItem>
                    <MenuItem value="cancelled">Cancelled</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Line Items */}
        <Card sx={{ mb: 3 }}>
          <CardHeader 
            title="Line Items" 
            action={
              <Button
                variant="outlined"
                size="small"
                startIcon={<AddIcon />}
                onClick={addLineItem}
              >
                Add Item
              </Button>
            }
          />
          <CardContent>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Description</TableCell>
                    <TableCell width={100}>Quantity</TableCell>
                    <TableCell width={120}>Unit Price</TableCell>
                    <TableCell width={80}>Tax %</TableCell>
                    <TableCell width={120}>Total</TableCell>
                    <TableCell width={50}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {formData.items?.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Autocomplete
                          options={products}
                          getOptionLabel={(option) => option.name}
                          onChange={(_, value) => handleProductSelect(index, value)}
                          renderInput={(params) => (
                            <TextField 
                              {...params} 
                              placeholder="Select product or enter description"
                              value={item.description}
                              onChange={(e) => updateLineItem(index, 'description', e.target.value)}
                            />
                          )}
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          type="number"
                          value={item.quantity}
                          onChange={(e) => updateLineItem(index, 'quantity', Number(e.target.value))}
                          inputProps={{ min: 0, step: 0.01 }}
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          type="number"
                          value={item.unit_price}
                          onChange={(e) => updateLineItem(index, 'unit_price', Number(e.target.value))}
                          inputProps={{ min: 0, step: 0.01 }}
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          type="number"
                          value={item.tax_rate || 0}
                          onChange={(e) => updateLineItem(index, 'tax_rate', Number(e.target.value))}
                          inputProps={{ min: 0, max: 100, step: 0.01 }}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {formatCurrency(item.line_total + (item.tax_amount || 0))}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => removeLineItem(index)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                  {!formData.items?.length && (
                    <TableRow>
                      <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                        <Typography color="text.secondary">
                          No items added. Click "Add Item" to start.
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            {/* Totals */}
            {(formData.items?.length || 0) > 0 && (
              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                <Box sx={{ minWidth: 300 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Subtotal:</Typography>
                    <Typography>{formatCurrency(formData.subtotal || 0)}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Tax:</Typography>
                    <Typography>{formatCurrency(formData.tax_total || 0)}</Typography>
                  </Box>
                  <Divider sx={{ my: 1 }} />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="h6">Total:</Typography>
                    <Typography variant="h6">{formatCurrency(formData.total_amount || 0)}</Typography>
                  </Box>
                </Box>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Notes and Terms */}
        <Card>
          <CardHeader title="Additional Information" />
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={4}
                  value={formData.notes}
                  onChange={(e) => handleFieldChange('notes', e.target.value)}
                  placeholder="Internal notes (not shown to customer)"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Terms & Conditions"
                  multiline
                  rows={4}
                  value={formData.terms}
                  onChange={(e) => handleFieldChange('terms', e.target.value)}
                  placeholder="Payment terms and conditions"
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Box>
      
      {/* Footer */}
      <Box sx={{ 
        p: 2, 
        borderTop: 1, 
        borderColor: 'divider',
        display: 'flex',
        justifyContent: 'space-between'
      }}>
        <Button
          variant="outlined"
          onClick={handleCancel}
          startIcon={<ArrowBackIcon />}
        >
          Cancel
        </Button>
        <Box sx={{ display: 'flex', gap: 2 }}>
        <Button
          variant="outlined"
            onClick={() => handleSave(false)}
            disabled={loading}
            startIcon={<SaveIcon />}
          >
            Save Draft
        </Button>
        <Button
          variant="contained"
            onClick={() => handleSave(true)}
            disabled={loading}
          startIcon={<SaveIcon />}
        >
            {loading ? 'Saving...' : 'Save & Close'}
        </Button>
        </Box>
      </Box>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        open={confirmation.open}
        onClose={hideConfirmation}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        type={confirmation.type}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        showCancel={confirmation.showCancel}
        confirmColor={confirmation.confirmColor}
        autoClose={confirmation.autoClose}
        autoCloseDelay={confirmation.autoCloseDelay}
      />
    </Box>
  );
};

export default CreateInvoicePage; 