import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  Button,
  Autocomplete,
  IconButton,
  Divider,
  Paper,
  Alert,
  Snackbar,
  MenuItem,
  Chip,
  Avatar,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  SaveAlt as SaveAltIcon,
  Print as PrintIcon,
  Cancel as CancelIcon,
  Receipt as ReceiptIcon,
  Description as DescriptionIcon,
  Assignment as AssignmentIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import dayjs from 'dayjs';
import { StandardDatePicker } from '../../../shared/components';
import VendorBillLineTable, { VendorBillLineItem, ProductOption } from '../../../shared/components/VendorBillLineTable';
import { formatCurrency } from '../../../shared/utils/formatters';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';
import { vendorService, type Vendor } from '../../../services/vendor.service';
import { loadChartOfAccountsFast } from '../../../services/gl.service';
import type { Account } from '../../../shared/types/gl.types';

// Mock interfaces (will be replaced with actual service calls)

interface VendorBillFormData {
  vendor_id: number | null;
  liability_account_id: number | null;
  bill_date: string;
  due_date: string;
  reference_number: string;
  status: 'draft' | 'approved' | 'paid' | 'rejected';
  payment_terms: string;
  notes: string;
  line_items: VendorBillLineItem[];
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  source_type?: 'manual' | 'grn' | 'po' | 'return_note';
  source_document_id?: number;
}

const CreateVendorBillPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const location = useLocation();
  const { currencyInfo, loading: currencyLoading } = useCurrencyInfo();

  // Determine mode and source type from URL
  const isEditing = Boolean(id);
  const isFromGRN = location.pathname.includes('create-from-grn');
  const isFromPO = location.pathname.includes('create-from-po');
  const isFromReturn = location.pathname.includes('create-from-return');

  // State
  const [loading, setLoading] = useState(false);
  const [vendorsLoading, setVendorsLoading] = useState(true);
  const [accountsLoading, setAccountsLoading] = useState(true);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [products, setProducts] = useState<ProductOption[]>([]);
  const [liabilityAccounts, setLiabilityAccounts] = useState<Account[]>([]);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'warning' | 'info',
  });

  // Form data
  const [formData, setFormData] = useState<VendorBillFormData>({
    vendor_id: null,
    liability_account_id: null,
    bill_date: dayjs().format('YYYY-MM-DD'),
    due_date: dayjs().add(30, 'day').format('YYYY-MM-DD'),
    reference_number: '',
    status: 'draft',
    payment_terms: 'Net 30',
    notes: '',
    line_items: [
      {
        id: '1',
        product_id: null,
        item_description: '',
        quantity: 1,
        unit_price: 0,
        line_total: 0,
        tax_rate: 0,
        tax_amount: 0,
      },
      {
        id: '2',
        product_id: null,
        item_description: '',
        quantity: 1,
        unit_price: 0,
        line_total: 0,
        tax_rate: 0,
        tax_amount: 0,
      },
      {
        id: '3',
        product_id: null,
        item_description: '',
        quantity: 1,
        unit_price: 0,
        line_total: 0,
        tax_rate: 0,
        tax_amount: 0,
      },
      {
        id: '4',
        product_id: null,
        item_description: '',
        quantity: 1,
        unit_price: 0,
        line_total: 0,
        tax_rate: 0,
        tax_amount: 0,
      },
    ],
    subtotal: 0,
    tax_amount: 0,
    total_amount: 0,
    source_type: isFromGRN ? 'grn' : isFromPO ? 'po' : isFromReturn ? 'return_note' : 'manual',
  });

  // Load vendors from service
  useEffect(() => {
    const loadVendors = async () => {
      try {
        setVendorsLoading(true);
        console.log('Loading vendors from API...');
        console.log('Auth token:', localStorage.getItem('token') ? 'Present' : 'Missing');

        const vendorData = await vendorService.getVendorsForDropdown();
        console.log('Final vendor response:', vendorData);
        console.log('Number of vendors loaded:', vendorData.length);
        setVendors(vendorData);

        if (vendorData.length === 0) {
          setSnackbar({
            open: true,
            message: 'No vendors found. Please add vendors first.',
            severity: 'warning',
          });
        } else {
          setSnackbar({
            open: true,
            message: `Successfully loaded ${vendorData.length} vendors`,
            severity: 'success',
          });
        }
      } catch (error) {
        console.error('Failed to load vendors:', error);
        setSnackbar({
          open: true,
          message: `Failed to load vendors: ${error instanceof Error ? error.message : 'Unknown error'}`,
          severity: 'error',
        });
      } finally {
        setVendorsLoading(false);
      }
    };

    loadVendors();

    // Load liability accounts from COA
    const loadLiabilityAccounts = async () => {
      try {
        setAccountsLoading(true);
        console.log('Loading liability accounts from COA...');

        const coaData = await loadChartOfAccountsFast();
        console.log('Raw COA response:', coaData);
        console.log('Total accounts in COA:', coaData.accounts?.length || 0);

        // Debug: Check all account types available
        const accountTypes = [...new Set(coaData.accounts?.map((acc: Account) => acc.account_type_name) || [])];
        console.log('Available account types:', accountTypes);

        // Debug: Check all accounts with their types
        console.log('All accounts with types:', coaData.accounts?.map((acc: Account) => ({
          id: acc.id,
          number: acc.account_number,
          name: acc.account_name,
          type: acc.account_type_name,
          active: acc.is_active,
          header: acc.is_header_account
        })));

        // Try multiple filtering approaches
        let liabilityAccounts = coaData.accounts?.filter((account: Account) =>
          account.account_type_name === 'Liability' &&
          account.is_active &&
          !account.is_header_account
        ) || [];

        console.log('Strict filter result:', liabilityAccounts.length);

        // If no results, try looser filtering
        if (liabilityAccounts.length === 0) {
          // Try case-insensitive
          liabilityAccounts = coaData.accounts?.filter((account: Account) =>
            account.account_type_name?.toLowerCase().includes('liability') &&
            account.is_active
          ) || [];
          console.log('Case-insensitive filter result:', liabilityAccounts.length);
        }

        // If still no results, try even looser filtering
        if (liabilityAccounts.length === 0) {
          // Try accounts starting with 2 (typical liability account numbers)
          liabilityAccounts = coaData.accounts?.filter((account: Account) =>
            account.account_number?.startsWith('2') &&
            account.is_active &&
            !account.is_header_account
          ) || [];
          console.log('Account number 2xxx filter result:', liabilityAccounts.length);
        }

        console.log('Final filtered liability accounts:', liabilityAccounts);
        console.log('Number of liability accounts loaded:', liabilityAccounts.length);
        setLiabilityAccounts(liabilityAccounts);

        if (liabilityAccounts.length === 0) {
          setSnackbar({
            open: true,
            message: 'No liability accounts found. Please set up Chart of Accounts first.',
            severity: 'warning',
          });
        }
      } catch (error) {
        console.error('Failed to load liability accounts:', error);
        setSnackbar({
          open: true,
          message: `Failed to load liability accounts: ${error instanceof Error ? error.message : 'Unknown error'}`,
          severity: 'error',
        });
      } finally {
        setAccountsLoading(false);
      }
    };

    loadLiabilityAccounts();

    // Mock products
    setProducts([
      { id: 1, name: 'Office Supplies', description: 'General office supplies', cost_price: 25.00, expense_account_code: '5100-Office' },
      { id: 2, name: 'Computer Equipment', description: 'IT hardware and equipment', cost_price: 500.00, expense_account_code: '5200-Equipment' },
      { id: 3, name: 'Consulting Services', description: 'Professional consulting', cost_price: 150.00, expense_account_code: '5300-Services' },
      { id: 4, name: 'Raw Materials', description: 'Manufacturing materials', cost_price: 75.00, expense_account_code: '5010-COGS' },
    ]);
  }, []);

  // Handle form field changes
  const handleFieldChange = (field: keyof VendorBillFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle vendor selection
  const handleVendorChange = (vendor: Vendor | null) => {
    if (vendor) {
      setFormData(prev => ({
        ...prev,
        vendor_id: vendor.id,
        payment_terms: `Net ${vendor.payment_terms}` || 'Net 30'
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        vendor_id: null,
        payment_terms: 'Net 30'
      }));
    }
  };

  // Handle line item changes
  const handleLineChange = (lineId: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      line_items: prev.line_items.map(item =>
        item.id === lineId ? { ...item, [field]: value } : item
      )
    }));
    
    // Recalculate totals after a short delay
    setTimeout(calculateTotals, 100);
  };

  // Add new line item
  const handleAddLine = () => {
    const newId = (formData.line_items.length + 1).toString();
    const newLine: VendorBillLineItem = {
      id: newId,
      product_id: null,
      item_description: '',
      quantity: 1,
      unit_price: 0,
      line_total: 0,
      tax_rate: 0,
      tax_amount: 0,
    };

    setFormData(prev => ({
      ...prev,
      line_items: [...prev.line_items, newLine]
    }));
  };

  // Remove line item
  const handleRemoveLine = (lineId: string) => {
    setFormData(prev => ({
      ...prev,
      line_items: prev.line_items.filter(item => item.id !== lineId)
    }));
    
    setTimeout(calculateTotals, 100);
  };

  // Calculate totals
  const calculateTotals = () => {
    const subtotal = formData.line_items.reduce((sum, item) => sum + (item.line_total || 0), 0);
    const taxAmount = formData.line_items.reduce((sum, item) => sum + (item.tax_amount || 0), 0);
    const totalAmount = subtotal + taxAmount;

    setFormData(prev => ({
      ...prev,
      subtotal,
      tax_amount: taxAmount,
      total_amount: totalAmount
    }));
  };

  // Handle save
  const handleSave = async (saveAndClose = false) => {
    try {
      setLoading(true);

      // Validate required fields
      if (!formData.vendor_id) {
        setSnackbar({
          open: true,
          message: 'Please select a vendor',
          severity: 'error',
        });
        return;
      }

      if (!formData.liability_account_id) {
        setSnackbar({
          open: true,
          message: 'Please select a liability account',
          severity: 'error',
        });
        return;
      }

      // Filter out empty line items
      const validLineItems = formData.line_items.filter(item => 
        item.item_description.trim() !== '' || item.quantity > 0 || item.unit_price > 0
      );

      if (validLineItems.length === 0) {
        setSnackbar({
          open: true,
          message: 'Please add at least one line item',
          severity: 'error',
        });
        return;
      }

      // TODO: Replace with actual API call
      console.log('Saving vendor bill:', { ...formData, line_items: validLineItems });

      setSnackbar({
        open: true,
        message: isEditing ? 'Vendor bill updated successfully!' : 'Vendor bill created successfully!',
        severity: 'success',
      });

      if (saveAndClose) {
        setTimeout(() => {
          navigate('/dashboard/purchases/vendor-bills');
        }, 1500);
      }

    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to save vendor bill. Please try again.',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const getSourceInfo = () => {
    if (isFromGRN) return { icon: <ReceiptIcon />, label: 'From GRN', color: '#4caf50' };
    if (isFromPO) return { icon: <DescriptionIcon />, label: 'From Purchase Order', color: '#2196f3' };
    if (isFromReturn) return { icon: <AssignmentIcon />, label: 'From Return Note', color: '#ff9800' };
    return { icon: <PersonIcon />, label: 'Manual Entry', color: '#9c27b0' };
  };

  const sourceInfo = getSourceInfo();

  return (
    <>
      {/* Full Screen Layout - Like Cash Payment Form */}
      <Box
        sx={{
          position: 'fixed',
          top: '64px', // Account for navigation bar height
          left: 0,
          right: 0,
          bottom: 0,
          bgcolor: 'background.default',
          zIndex: 1200,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {/* Header */}
        <Box
          sx={{
            bgcolor: 'background.paper',
            borderBottom: 1,
            borderColor: 'divider',
            p: 2,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold" color="primary">
              {isEditing ? 'EDIT VENDOR BILL' : 'CREATE VENDOR BILL'}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <Avatar sx={{ bgcolor: sourceInfo.color, width: 20, height: 20, mr: 1 }}>
                {sourceInfo.icon}
              </Avatar>
              <Typography variant="body1" color="text.secondary">
                {sourceInfo.label} - {isEditing ? 'Modify vendor bill details' : 'Record vendor bills and payables efficiently'}
              </Typography>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
            {/* Amount Display */}
            <Box sx={{ textAlign: 'right', minWidth: '200px' }}>
              <Typography variant="h4" color="primary" sx={{ fontWeight: 'bold' }}>
                {formatCurrency(formData.total_amount)}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Total Bill Amount
              </Typography>
            </Box>
            <IconButton
              onClick={() => navigate('/dashboard/purchases/vendor-bills')}
              sx={{
                color: 'text.primary',
                '&:hover': {
                  backgroundColor: 'action.hover',
                  color: 'error.main'
                }
              }}
            >
              <ArrowBackIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Content */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
          <Grid container spacing={3}>
            {/* Main Form - Full Width */}
            <Grid item xs={12}>
            <Card sx={{ borderRadius: '12px', boxShadow: '0 2px 12px rgba(0,0,0,0.08)', mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
                  Bill Information
                </Typography>
                
                <Grid container spacing={3}>
                  {/* Vendor Selection */}
                  <Grid item xs={12} md={6}>
                    <Autocomplete
                      options={vendors}
                      loading={vendorsLoading}
                      noOptionsText={vendorsLoading ? "Loading vendors..." : "No vendors found"}
                      getOptionLabel={(option) => option.display_name}
                      value={vendors.find(v => v.id === formData.vendor_id) || null}
                      onChange={(_, newValue) => handleVendorChange(newValue)}
                      renderOption={(props, option) => (
                        <Box component="li" {...props}>
                          <Box>
                            <Typography variant="body1" sx={{ fontWeight: 600 }}>
                              {option.display_name}
                            </Typography>
                            {option.email && (
                              <Typography variant="body2" color="text.secondary">
                                {option.email}
                              </Typography>
                            )}
                            {option.phone && (
                              <Typography variant="caption" color="text.secondary">
                                Phone: {option.phone}
                              </Typography>
                            )}
                          </Box>
                        </Box>
                      )}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Vendor *"
                          required
                          fullWidth
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: '8px',
                            },
                          }}
                        />
                      )}
                    />
                  </Grid>

                  {/* Reference Number */}
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Reference Number"
                      value={formData.reference_number}
                      onChange={(e) => handleFieldChange('reference_number', e.target.value)}
                      placeholder="Vendor's invoice number"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '8px',
                        },
                      }}
                    />
                  </Grid>

                  {/* Bill Date */}
                  <Grid item xs={12} md={4}>
                    <StandardDatePicker
                      label="Bill Date *"
                      value={dayjs(formData.bill_date)}
                      onChange={(date) => handleFieldChange('bill_date', date?.format('YYYY-MM-DD'))}
                      required
                      businessContext="general"
                      showQuickActions
                      dateFormat="DD/MM/YYYY"
                    />
                  </Grid>

                  {/* Due Date */}
                  <Grid item xs={12} md={4}>
                    <StandardDatePicker
                      label="Due Date *"
                      value={dayjs(formData.due_date)}
                      onChange={(date) => handleFieldChange('due_date', date?.format('YYYY-MM-DD'))}
                      required
                      businessContext="general"
                      showQuickActions
                      dateFormat="DD/MM/YYYY"
                    />
                  </Grid>

                  {/* Status */}
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      select
                      label="Status"
                      value={formData.status}
                      onChange={(e) => handleFieldChange('status', e.target.value)}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '8px',
                        },
                      }}
                    >
                      <MenuItem value="draft">Draft</MenuItem>
                      <MenuItem value="approved">Approved</MenuItem>
                      <MenuItem value="paid">Paid</MenuItem>
                      <MenuItem value="rejected">Rejected</MenuItem>
                    </TextField>
                  </Grid>

                  {/* Liability Account (Credit Account) */}
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      select
                      label="Liability Account *"
                      value={formData.liability_account_id || ''}
                      onChange={(e) => handleFieldChange('liability_account_id', Number(e.target.value))}
                      required
                      disabled={accountsLoading}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '8px',
                        },
                      }}
                    >
                      {accountsLoading ? (
                        <MenuItem disabled>
                          <em>Loading liability accounts...</em>
                        </MenuItem>
                      ) : liabilityAccounts.length === 0 ? (
                        <MenuItem disabled>
                          <em>No liability accounts found</em>
                        </MenuItem>
                      ) : (
                        liabilityAccounts.map((account) => (
                          <MenuItem key={account.id} value={account.id}>
                            <Box>
                              <Typography variant="body1" sx={{ fontWeight: 600, fontSize: '1rem' }}>
                                {account.account_number} - {account.account_name}
                              </Typography>
                              <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                                {account.account_type_name} | Balance: {formatCurrency(account.current_balance || 0)}
                              </Typography>
                            </Box>
                          </MenuItem>
                        ))
                      )}
                    </TextField>
                  </Grid>

                  {/* Payment Terms */}
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Payment Terms"
                      value={formData.payment_terms}
                      onChange={(e) => handleFieldChange('payment_terms', e.target.value)}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '8px',
                        },
                      }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            {/* Line Items */}
            <Card sx={{ borderRadius: '12px', boxShadow: '0 2px 12px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ mb: 2, fontWeight: 600 }}>
                  Line Items
                </Typography>
                
                <VendorBillLineTable
                  lines={formData.line_items}
                  products={products}
                  onLineChange={handleLineChange}
                  onAddLine={handleAddLine}
                  onRemoveLine={handleRemoveLine}
                  currencySymbol={currencyInfo?.functional_currency_symbol || '$'}
                  minLines={4}
                  tableHeight="400px"
                  showTaxColumn={true}
                  showAccountColumn={true}
                />
              </CardContent>
            </Card>

            {/* Summary and Notes Section */}
            <Grid container spacing={3} sx={{ mt: 2 }}>
              {/* Notes */}
              <Grid item xs={12} md={8}>
                <Card sx={{ borderRadius: '12px', boxShadow: '0 2px 12px rgba(0,0,0,0.08)' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom sx={{ mb: 2, fontWeight: 600 }}>
                      Notes
                    </Typography>
                    <TextField
                      fullWidth
                      multiline
                      rows={3}
                      value={formData.notes}
                      onChange={(e) => handleFieldChange('notes', e.target.value)}
                      placeholder="Add any notes or comments..."
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '8px',
                        },
                      }}
                    />
                  </CardContent>
                </Card>
              </Grid>

              {/* Bill Summary */}
              <Grid item xs={12} md={4}>
                <Card sx={{ borderRadius: '12px', boxShadow: '0 2px 12px rgba(0,0,0,0.08)' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom sx={{ mb: 2, fontWeight: 600 }}>
                      Bill Summary
                    </Typography>

                    <Box sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2" color="text.secondary">Subtotal:</Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {formatCurrency(formData.subtotal)}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2" color="text.secondary">Tax:</Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {formatCurrency(formData.tax_amount)}
                        </Typography>
                      </Box>
                      <Divider sx={{ my: 1 }} />
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="h6" fontWeight="bold">Total:</Typography>
                        <Typography variant="h6" fontWeight="bold" color="primary">
                          {formatCurrency(formData.total_amount)}
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        </Box>

        {/* Footer Actions - Like Cash Payment Form */}
        <Box
          sx={{
            bgcolor: 'background.paper',
            borderTop: 1,
            borderColor: 'divider',
            p: 2,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Button
            startIcon={<CancelIcon />}
            onClick={() => navigate('/dashboard/purchases/vendor-bills')}
            color="inherit"
          >
            Cancel
          </Button>

          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              startIcon={<PrintIcon />}
              variant="outlined"
              color="inherit"
            >
              Print
            </Button>
            <Button
              startIcon={<SaveIcon />}
              variant="outlined"
              onClick={() => handleSave(false)}
              disabled={loading}
            >
              {isEditing ? 'Update' : 'Save'}
            </Button>
            <Button
              startIcon={<SaveAltIcon />}
              variant="contained"
              onClick={() => handleSave(true)}
              disabled={loading}
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                }
              }}
            >
              {isEditing ? 'Update and Close' : 'Save and Close'}
            </Button>
          </Box>
        </Box>
      </Box>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert 
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))} 
          severity={snackbar.severity}
          sx={{ 
            fontSize: '1rem',
            fontWeight: 'bold',
            minWidth: '400px'
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default CreateVendorBillPage;
