import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  Button,
  Autocomplete,
  IconButton,
  Divider,
  Paper,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import dayjs from 'dayjs';
import { vendorBillService, VendorBill, VendorBillItem } from '../../../services/vendor-bill.service';
import { vendorService, Vendor } from '../../../services/vendor.service';
import { formatCurrency } from '../../../shared/utils/formatters';
import ConfirmationDialog from '../../../shared/components/ConfirmationDialog';
import { useConfirmation } from '../../../shared/hooks/useConfirmation';
import { StandardDatePicker } from '../../../shared/components';

const CreateVendorBillPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditing = Boolean(id);
  const { confirmation, showSuccess, showError, hideConfirmation } = useConfirmation();

  // State
  const [loading, setLoading] = useState(false);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [products, setProducts] = useState<any[]>([]);

  const [formData, setFormData] = useState<Partial<VendorBill>>({
    vendor: 0,
    bill_date: dayjs().format('YYYY-MM-DD'),
    due_date: dayjs().add(30, 'day').format('YYYY-MM-DD'),
    status: 'draft',
    items: [
      {
        description: '',
        quantity: 1,
        unit_cost: 0,
        line_total: 0,
      }
    ],
    subtotal: 0,
    tax_total: 0,
    total_amount: 0,
    notes: '',
    terms: '',
  });

  // Load data
  useEffect(() => {
    loadVendors();
    if (isEditing && id) {
      loadVendorBill(parseInt(id));
    }
  }, [id, isEditing]);

  const loadVendors = async () => {
    try {
      const response = await vendorService.getVendors();
      setVendors(response.results || response);
    } catch (error) {
      console.error('Failed to load vendors:', error);
    }
  };

  const loadVendorBill = async (billId: number) => {
    try {
      setLoading(true);
      const bill = await vendorBillService.getVendorBill(billId);
      setFormData(bill);
    } catch (error) {
      console.error('Failed to load vendor bill:', error);
      showError('Load Failed', 'Failed to load vendor bill');
    } finally {
      setLoading(false);
    }
  };

  // Handle form field changes
  const handleFieldChange = (field: keyof VendorBill, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle item changes
  const handleItemChange = (index: number, field: keyof VendorBillItem, value: any) => {
    const updatedItems = [...(formData.items || [])];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value
    };

    // Calculate line total
    if (field === 'quantity' || field === 'unit_cost') {
      const quantity = field === 'quantity' ? value : updatedItems[index].quantity;
      const unitCost = field === 'unit_cost' ? value : updatedItems[index].unit_cost;
      updatedItems[index].line_total = quantity * unitCost;
    }

    setFormData(prev => ({
      ...prev,
      items: updatedItems
    }));

    // Recalculate totals
    calculateTotals(updatedItems);
  };

  // Add new item
  const addItem = () => {
    const newItem: VendorBillItem = {
      description: '',
      quantity: 1,
      unit_cost: 0,
      line_total: 0,
    };

    setFormData(prev => ({
      ...prev,
      items: [...(prev.items || []), newItem]
    }));
  };

  // Remove item
  const removeItem = (index: number) => {
    const updatedItems = (formData.items || []).filter((_, i) => i !== index);
    setFormData(prev => ({
      ...prev,
      items: updatedItems
    }));
    calculateTotals(updatedItems);
  };

  // Calculate totals
  const calculateTotals = (items: VendorBillItem[]) => {
    const subtotal = items.reduce((sum, item) => sum + (item.line_total || 0), 0);
    const taxTotal = items.reduce((sum, item) => sum + (item.tax_amount || 0), 0);
    const totalAmount = subtotal + taxTotal;

    setFormData(prev => ({
      ...prev,
      subtotal,
      tax_total: taxTotal,
      total_amount: totalAmount
    }));
  };

  // Handle save
  const handleSave = async (saveAndClose = false) => {
    try {
      setLoading(true);

      // Validate required fields
      if (!formData.vendor || formData.vendor === 0) {
        showError('Validation Error', 'Please select a vendor');
        return;
      }

      if (!formData.items || formData.items.length === 0) {
        showError('Validation Error', 'Please add at least one item');
        return;
      }

      const billData = {
        ...formData,
        items: formData.items?.filter(item => item.description.trim() !== '') || []
      };

      if (isEditing && id) {
        await vendorBillService.updateVendorBill(parseInt(id), billData);
        showSuccess(
          'Bill Updated!',
          'Vendor bill has been updated successfully.'
        );
      } else {
        await vendorBillService.createVendorBill(billData as any);
        showSuccess(
          'Bill Created!',
          'Vendor bill has been created successfully.'
        );
      }

      if (saveAndClose) {
        setTimeout(() => {
          navigate('/dashboard/purchases/vendor-bills');
        }, 1500);
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save vendor bill';
      showError('Save Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={() => navigate('/dashboard/purchases/vendor-bills')} sx={{ mr: 2 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" component="h1">
          {isEditing ? 'Edit Vendor Bill' : 'Create Vendor Bill'}
        </Typography>
      </Box>

      {/* Form */}
      <Card>
        <CardContent>
          <Grid container spacing={3}>
            {/* Vendor Selection */}
            <Grid item xs={12} md={6}>
              <Autocomplete
                options={vendors}
                getOptionLabel={(option) => option.display_name}
                value={vendors.find(v => v.id === formData.vendor) || null}
                onChange={(_, newValue) => handleFieldChange('vendor', newValue?.id || 0)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Vendor *"
                    required
                    fullWidth
                  />
                )}
              />
            </Grid>

            {/* Reference Number */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Reference Number"
                value={formData.reference_number || ''}
                onChange={(e) => handleFieldChange('reference_number', e.target.value)}
              />
            </Grid>

            {/* Bill Date */}
            <Grid item xs={12} md={3}>
              <StandardDatePicker
                label="Bill Date"
                value={dayjs(formData.bill_date)}
                onChange={(date) => handleFieldChange('bill_date', date?.format('YYYY-MM-DD'))}
                required
                businessContext="general"
                showQuickActions
                dateFormat="DD/MM/YYYY"
              />
            </Grid>

            {/* Due Date */}
            <Grid item xs={12} md={3}>
              <StandardDatePicker
                label="Due Date"
                value={dayjs(formData.due_date)}
                onChange={(date) => handleFieldChange('due_date', date?.format('YYYY-MM-DD'))}
                required
                businessContext="general"
                showQuickActions
                dateFormat="DD/MM/YYYY"
              />
            </Grid>

            {/* Status */}
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                select
                label="Status"
                value={formData.status || 'draft'}
                onChange={(e) => handleFieldChange('status', e.target.value)}
                SelectProps={{ native: true }}
              >
                <option value="draft">Draft</option>
                <option value="pending">Pending</option>
                <option value="paid">Paid</option>
                <option value="overdue">Overdue</option>
                <option value="cancelled">Cancelled</option>
              </TextField>
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          {/* Items Section */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Items</Typography>
              <Button
                startIcon={<AddIcon />}
                onClick={addItem}
                variant="outlined"
                size="small"
              >
                Add Item
              </Button>
            </Box>

            {/* Items Table */}
            {formData.items?.map((item, index) => (
              <Paper key={index} sx={{ p: 2, mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Description"
                      value={item.description}
                      onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                      multiline
                      rows={2}
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <TextField
                      fullWidth
                      label="Quantity"
                      type="number"
                      value={item.quantity}
                      onChange={(e) => handleItemChange(index, 'quantity', parseFloat(e.target.value) || 0)}
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <TextField
                      fullWidth
                      label="Unit Cost"
                      type="number"
                      value={item.unit_cost}
                      onChange={(e) => handleItemChange(index, 'unit_cost', parseFloat(e.target.value) || 0)}
                      inputProps={{ step: "0.01", min: "0" }}
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <TextField
                      fullWidth
                      label="Line Total"
                      value={formatCurrency(item.line_total)}
                      InputProps={{ readOnly: true }}
                    />
                  </Grid>
                  <Grid item xs={12} md={1}>
                    <IconButton
                      onClick={() => removeItem(index)}
                      color="error"
                      disabled={(formData.items?.length || 0) <= 1}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Grid>
                </Grid>
              </Paper>
            ))}
          </Box>

          <Divider sx={{ my: 3 }} />

          {/* Totals */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Notes"
                value={formData.notes || ''}
                onChange={(e) => handleFieldChange('notes', e.target.value)}
                multiline
                rows={3}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ textAlign: 'right' }}>
                <Typography variant="body1" sx={{ mb: 1 }}>
                  Subtotal: {formatCurrency(formData.subtotal || 0)}
                </Typography>
                <Typography variant="body1" sx={{ mb: 1 }}>
                  Tax: {formatCurrency(formData.tax_total || 0)}
                </Typography>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  Total: {formatCurrency(formData.total_amount || 0)}
                </Typography>
              </Box>
            </Grid>
          </Grid>

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
            <Button
              variant="outlined"
              onClick={() => navigate('/dashboard/purchases/vendor-bills')}
            >
              Cancel
            </Button>
            <Button
              variant="outlined"
              onClick={() => handleSave(false)}
              disabled={loading}
            >
              Save
            </Button>
            <Button
              variant="contained"
              onClick={() => handleSave(true)}
              disabled={loading}
            >
              Save and Close
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        open={confirmation.open}
        onClose={hideConfirmation}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        type={confirmation.type}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        showCancel={confirmation.showCancel}
        confirmColor={confirmation.confirmColor}
        autoClose={confirmation.autoClose}
        autoCloseDelay={confirmation.autoCloseDelay}
      />
    </Box>
  );
};

export default CreateVendorBillPage;
