from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from decimal import Decimal
import uuid


class PaymentTerm(models.Model):
    """Payment terms that can be used across vendors, purchase orders, etc."""
    
    name = models.CharField(max_length=100, unique=True, help_text="E.g., 'Net 30', 'Due on Receipt'")
    code = models.CharField(max_length=50, unique=True, help_text="E.g., 'net_30', 'due_on_receipt'")
    days = models.PositiveIntegerField(help_text="Number of days from invoice date")
    description = models.TextField(blank=True, null=True, help_text="Optional description")
    is_default = models.BooleanField(default=False, help_text="Is this the default payment term?")
    is_active = models.BooleanField(default=True, help_text="Is this payment term active?")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='purchase_payment_terms_created')
    
    class Meta:
        db_table = 'purchase_payment_terms'
        ordering = ['days', 'name']
        verbose_name = 'Payment Term'
        verbose_name_plural = 'Payment Terms'
    
    def __str__(self):
        return f"{self.name} ({self.days} days)"
    
    def save(self, *args, **kwargs):
        # Ensure only one default payment term
        if self.is_default:
            PaymentTerm.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


# Vendor model is now in contacts app - using contacts.Vendor instead


class PurchaseOrder(models.Model):
    """Purchase Order model following QuickBooks structure"""
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('acknowledged', 'Acknowledged'),
        ('partial', 'Partially Received'),
        ('received', 'Received'),
        ('closed', 'Closed'),
        ('cancelled', 'Cancelled'),
    ]
    
    # Basic Information
    po_id = models.UUIDField(default=uuid.uuid4, unique=True)
    po_number = models.CharField(max_length=50, unique=True)
    vendor = models.ForeignKey('contacts.Contact', on_delete=models.SET_NULL, null=True, blank=True, related_name='purchase_orders', help_text='Vendor from contacts system')
    
    # Dates
    po_date = models.DateField()
    expected_date = models.DateField(blank=True, null=True)
    
    # Buyer Information
    buyer_name = models.CharField(max_length=200, blank=True, null=True, help_text="Name of the buyer/purchaser")
    buyer_email = models.EmailField(blank=True, null=True, help_text="Email of the buyer/purchaser")
    buyer_phone = models.CharField(max_length=20, blank=True, null=True, help_text="Phone of the buyer/purchaser")
    
    # Financial Information
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    discount_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    amount_received = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    balance_due = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    
    # Settings
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    payment_terms = models.CharField(max_length=20, blank=True, null=True)
    
    # Additional Information
    reference_number = models.CharField(max_length=100, blank=True, null=True)
    memo = models.TextField(blank=True, null=True, help_text="Internal memo")
    notes = models.TextField(blank=True, null=True, help_text="Notes to vendor")
    
    # Shipping Information
    ship_to_address = models.TextField(blank=True, null=True)
    
    # Email Tracking
    email_sent = models.BooleanField(default=False)
    email_sent_date = models.DateTimeField(blank=True, null=True)
    acknowledged_date = models.DateTimeField(blank=True, null=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='purchase_orders_created')
    
    class Meta:
        db_table = 'purchase_orders'
        ordering = ['-po_date', '-created_at']
    
    def __str__(self):
        vendor_name = self.vendor.name if self.vendor else "No Vendor"
        return f"PO {self.po_number} - {vendor_name}"
    
    def calculate_totals(self):
        """Calculate totals from line items"""
        from decimal import Decimal
        
        line_items = self.line_items.all()
        
        # Calculate subtotal from line items
        self.subtotal = sum(item.line_total for item in line_items) or Decimal('0.00')
        
        # Calculate discount amount
        self.discount_amount = self.subtotal * (Decimal(str(self.discount_percent)) / 100)
        
        # Calculate tax amount from line items
        self.tax_amount = sum(item.tax_amount for item in line_items) or Decimal('0.00')
        
        # Calculate total amount
        self.total_amount = self.subtotal - self.discount_amount + self.tax_amount
        
        # Calculate balance due
        self.balance_due = self.total_amount - Decimal(str(self.amount_received))

    def save(self, *args, **kwargs):
        from decimal import Decimal
        
        # Auto-generate PO number if not provided
        if not self.po_number:
            last_po = PurchaseOrder.objects.filter(
                po_number__startswith='PO-'
            ).order_by('-created_at').first()
            
            if last_po:
                try:
                    last_number = int(last_po.po_number.split('-')[1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1
            
            self.po_number = f'PO-{new_number:06d}'
        
        # Ensure all decimal fields are Decimal type
        self.subtotal = Decimal(str(self.subtotal))
        self.discount_percent = Decimal(str(self.discount_percent))
        self.discount_amount = Decimal(str(self.discount_amount))
        self.tax_amount = Decimal(str(self.tax_amount))
        self.total_amount = Decimal(str(self.total_amount))
        self.amount_received = Decimal(str(self.amount_received))
        
        # Calculate balance due
        self.balance_due = self.total_amount - self.amount_received
        
        # Update status based on actual quantity receipt, not financial amounts
        # Don't auto-update status in save method - let it be controlled explicitly
        # by the GRN posting process to avoid conflicts
        
        # Only auto-set status for new POs or if explicitly changing financial amounts
        if self.pk is None:  # New PO
            if self.status not in ['draft', 'pending', 'sent', 'acknowledged', 'partial', 'received']:
                self.status = 'draft'
        
        super().save(*args, **kwargs)


class PurchaseOrderLineItem(models.Model):
    """Purchase Order line items for products and services"""
    
    purchase_order = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE, related_name='line_items')
    
    # Product/Service Link
    product = models.ForeignKey(
        'sales.Product', 
        on_delete=models.CASCADE, 
        related_name='purchase_line_items',
        null=True,
        blank=True,
        help_text="Link to product master for GL integration"
    )
    
    # Item details
    description = models.TextField(help_text="Auto-filled from product, can be overridden")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=1.00)
    unit_of_measure = models.CharField(max_length=20, default='pcs', help_text="Unit of measure (kg, L, pcs, etc.)")
    unit_price = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, help_text="Purchase cost per unit")
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    line_total = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    
    # Receiving information
    quantity_received = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    quantity_pending = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    
    # Tax information
    taxable = models.BooleanField(default=True)
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    
    # Additional information
    notes = models.TextField(blank=True, null=True, help_text="Line item specific notes")
    
    # Line item order
    line_order = models.PositiveIntegerField(default=0)
    
    class Meta:
        db_table = 'purchase_order_line_items'
        ordering = ['line_order']
    
    def save(self, *args, **kwargs):
        from decimal import Decimal
        
        # Auto-fill description from product if not provided
        if not self.description and self.product:
            self.description = self.product.name
        
        # Ensure all numeric fields are Decimal
        self.quantity = Decimal(str(self.quantity))
        self.unit_price = Decimal(str(self.unit_price))
        self.discount_percent = Decimal(str(self.discount_percent))
        self.tax_rate = Decimal(str(self.tax_rate))
        self.quantity_received = Decimal(str(self.quantity_received or 0))
        
        # Calculate line total
        subtotal = self.quantity * self.unit_price
        discount_amount = subtotal * (self.discount_percent / 100)
        self.line_total = subtotal - discount_amount
        
        # Calculate tax
        if self.taxable:
            self.tax_amount = self.line_total * (self.tax_rate / 100)
        else:
            self.tax_amount = Decimal('0.00')
        
        # Calculate pending quantity
        self.quantity_pending = self.quantity - self.quantity_received
        
        super().save(*args, **kwargs)
    
    def create_receipt_journal_entry(self):
        """Create GL journal entry when goods are received"""
        if not self.product or self.quantity_received <= 0:
            return None
            
        from gl.models import JournalEntry, JournalEntryLine
        
        # Calculate received amount
        received_value = self.quantity_received * self.unit_price
        
        # Create journal entry for goods receipt
        journal_entry = JournalEntry.objects.create(
            reference_number=f"GR-{self.purchase_order.po_number}-{self.id}",
            description=f"Goods Receipt: {self.product.name}",
            total_amount=received_value,
            entry_type='purchase_receipt'
        )
        
        # Debit Inventory Asset Account
        if self.product.inventory_asset_account_gl:
            JournalEntryLine.objects.create(
                journal_entry=journal_entry,
                account=self.product.inventory_asset_account_gl,
                description=f"Inventory Receipt: {self.product.name}",
                debit_amount=received_value,
                credit_amount=0
            )
        
        # Credit Accounts Payable (or Cash if paid immediately)
        # Note: This would need a payable account reference
        # For now, we'll leave this as a placeholder
        
        return journal_entry
    
    def update_product_inventory(self):
        """Update product inventory quantities and cost"""
        if self.product and self.quantity_received > 0:
            # Update inventory quantity
            self.product.quantity_on_hand += self.quantity_received
            
            # Update cost price with weighted average
            total_current_value = self.product.quantity_on_hand * self.product.cost_price
            new_purchase_value = self.quantity_received * self.unit_price
            total_quantity = self.product.quantity_on_hand + self.quantity_received
            
            if total_quantity > 0:
                self.product.cost_price = (total_current_value + new_purchase_value) / total_quantity
            
            self.product.quantity_on_hand = total_quantity
            self.product.save()


class VendorBill(models.Model):
    """Vendor Bill model for separate bill tracking (mirrors customer invoices)"""

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending', 'Pending'),
        ('paid', 'Paid'),
        ('overdue', 'Overdue'),
        ('cancelled', 'Cancelled'),
    ]

    # Basic Information
    bill_id = models.UUIDField(default=uuid.uuid4, unique=True)
    bill_number = models.CharField(max_length=50, unique=True)
    vendor = models.ForeignKey('contacts.Contact', on_delete=models.SET_NULL, null=True, blank=True, related_name='vendor_bills', help_text='Vendor from contacts system')

    # Dates
    bill_date = models.DateField()
    due_date = models.DateField()

    # Financial Information
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    tax_total = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    amount_paid = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    balance_due = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Settings
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')

    # Additional Information
    reference_number = models.CharField(max_length=100, blank=True, null=True, help_text="Vendor's bill/invoice number")
    notes = models.TextField(blank=True, null=True, help_text="Internal notes")
    terms = models.TextField(blank=True, null=True, help_text="Payment terms and conditions")

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='vendor_bills_created')

    class Meta:
        db_table = 'purchase_vendor_bills'
        ordering = ['-bill_date', '-created_at']
        verbose_name = 'Vendor Bill'
        verbose_name_plural = 'Vendor Bills'

    def __str__(self):
        vendor_name = self.vendor.display_name if self.vendor else "No Vendor"
        return f"Bill {self.bill_number} - {vendor_name}"

    def calculate_totals(self):
        """Calculate totals from line items"""
        from decimal import Decimal

        line_items = self.items.all()

        # Calculate subtotal from line items
        self.subtotal = sum(item.line_total for item in line_items) or Decimal('0.00')

        # Calculate tax amount from line items
        self.tax_total = sum(item.tax_amount for item in line_items) or Decimal('0.00')

        # Calculate total amount
        self.total_amount = self.subtotal + self.tax_total

        # Calculate balance due
        self.balance_due = self.total_amount - Decimal(str(self.amount_paid))

    def save(self, *args, **kwargs):
        from decimal import Decimal

        # Auto-generate bill number if not provided
        if not self.bill_number:
            last_bill = VendorBill.objects.filter(
                bill_number__startswith='BILL-'
            ).order_by('-created_at').first()

            if last_bill:
                try:
                    last_number = int(last_bill.bill_number.split('-')[1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1

            self.bill_number = f'BILL-{new_number:06d}'

        # Ensure all decimal fields are Decimal type
        self.subtotal = Decimal(str(self.subtotal))
        self.tax_total = Decimal(str(self.tax_total))
        self.total_amount = Decimal(str(self.total_amount))
        self.amount_paid = Decimal(str(self.amount_paid))

        # Calculate balance due
        self.balance_due = self.total_amount - self.amount_paid

        # Update status based on payment
        from datetime import date
        if self.balance_due <= 0 and self.total_amount > 0:
            self.status = 'paid'
        elif self.due_date < date.today() and self.balance_due > 0:
            self.status = 'overdue'
        elif self.status == 'draft' and self.total_amount > 0:
            self.status = 'pending'

        super().save(*args, **kwargs)


class VendorBillItem(models.Model):
    """Vendor Bill line items for products and services"""

    vendor_bill = models.ForeignKey(VendorBill, on_delete=models.CASCADE, related_name='items')

    # Product/Service Link
    product = models.ForeignKey(
        'sales.Product',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='vendor_bill_items',
        help_text="Link to product master"
    )

    # Item details
    description = models.TextField(help_text="Item description")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=1.00)
    unit_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, help_text="Cost per unit")
    line_total = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Tax information
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Line item order
    line_order = models.PositiveIntegerField(default=0)

    class Meta:
        db_table = 'purchase_vendor_bill_items'
        ordering = ['line_order']
        verbose_name = 'Vendor Bill Item'
        verbose_name_plural = 'Vendor Bill Items'

    def save(self, *args, **kwargs):
        from decimal import Decimal

        # Auto-fill description from product if not provided
        if not self.description and self.product:
            self.description = self.product.name

        # Ensure all numeric fields are Decimal
        self.quantity = Decimal(str(self.quantity))
        self.unit_cost = Decimal(str(self.unit_cost))
        self.tax_rate = Decimal(str(self.tax_rate))

        # Calculate line total
        self.line_total = self.quantity * self.unit_cost

        # Calculate tax
        self.tax_amount = self.line_total * (self.tax_rate / 100)

        super().save(*args, **kwargs)


class VendorPayment(models.Model):
    """Payment model for tracking vendor payments"""
    
    PAYMENT_METHOD_CHOICES = [
        ('cash', 'Cash'),
        ('check', 'Check'),
        ('credit_card', 'Credit Card'),
        ('bank_transfer', 'Bank Transfer'),
        ('online', 'Online Payment'),
        ('other', 'Other'),
    ]
    
    payment_id = models.UUIDField(default=uuid.uuid4, unique=True)
    vendor = models.ForeignKey('contacts.Contact', on_delete=models.SET_NULL, null=True, blank=True, related_name='vendor_payments', help_text='Vendor from contacts system')
    purchase_order = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE, related_name='payments', blank=True, null=True)
    
    # Payment details
    payment_date = models.DateField()
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, default='bank_transfer')
    reference_number = models.CharField(max_length=100, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    
    # Account information
    paid_from_account = models.CharField(max_length=200, blank=True, null=True)  # Link to GL account
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='vendor_payments_created')
    
    class Meta:
        db_table = 'purchase_vendor_payments'
        ordering = ['-payment_date']
    
    def __str__(self):
        return f"Payment {self.payment_id} - {self.vendor.display_name} - {self.amount}"


# Signals to update Purchase Order totals when line items change
@receiver(post_save, sender=PurchaseOrderLineItem)
def update_po_totals_on_line_item_save(sender, instance, **kwargs):
    """Update Purchase Order totals when a line item is saved"""
    po = instance.purchase_order
    po.calculate_totals()
    # Use update to avoid triggering the save method again
    PurchaseOrder.objects.filter(pk=po.pk).update(
        subtotal=po.subtotal,
        discount_amount=po.discount_amount,
        tax_amount=po.tax_amount,
        total_amount=po.total_amount,
        balance_due=po.balance_due
    )


@receiver(post_delete, sender=PurchaseOrderLineItem)
def update_po_totals_on_line_item_delete(sender, instance, **kwargs):
    """Update Purchase Order totals when a line item is deleted"""
    po = instance.purchase_order
    po.calculate_totals()
    # Use update to avoid triggering the save method again
    PurchaseOrder.objects.filter(pk=po.pk).update(
        subtotal=po.subtotal,
        discount_amount=po.discount_amount,
        tax_amount=po.tax_amount,
        total_amount=po.total_amount,
        balance_due=po.balance_due
    )


# Signals to update Vendor Bill totals when line items change
@receiver(post_save, sender=VendorBillItem)
def update_vendor_bill_totals_on_line_item_save(sender, instance, **kwargs):
    """Update Vendor Bill totals when a line item is saved"""
    bill = instance.vendor_bill
    bill.calculate_totals()
    # Use update to avoid triggering the save method again
    VendorBill.objects.filter(pk=bill.pk).update(
        subtotal=bill.subtotal,
        tax_total=bill.tax_total,
        total_amount=bill.total_amount,
        balance_due=bill.balance_due
    )


@receiver(post_delete, sender=VendorBillItem)
def update_vendor_bill_totals_on_line_item_delete(sender, instance, **kwargs):
    """Update Vendor Bill totals when a line item is deleted"""
    bill = instance.vendor_bill
    bill.calculate_totals()
    # Use update to avoid triggering the save method again
    VendorBill.objects.filter(pk=bill.pk).update(
        subtotal=bill.subtotal,
        tax_total=bill.tax_total,
        total_amount=bill.total_amount,
        balance_due=bill.balance_due
    )
