from rest_framework import serializers
from .models import <PERSON>ur<PERSON><PERSON><PERSON><PERSON>, PurchaseOrderLineItem, VendorBill, VendorBillItem
from contacts.models import Contact, Vendor


class VendorSerializer(serializers.ModelSerializer):
    """Serializer for Vendor model with frontend-compatible fields"""
    # Explicitly include id field (which is the contact_id since contact is the primary key)
    id = serializers.IntegerField(source='contact.id', read_only=True)
    
    # Add fields expected by frontend
    display_name = serializers.CharField(source='contact.name', read_only=True)
    vendor_id = serializers.CharField(source='vendor_code', read_only=True)
    email = serializers.CharField(source='contact.email', read_only=True)
    phone = serializers.CharField(source='contact.phone', read_only=True)
    created_at = serializers.CharField(source='contact.created_at', read_only=True)
    updated_at = serializers.CharField(source='contact.updated_at', read_only=True)
    
    # Add default values for fields that don't exist in Contact/Vendor model but frontend expects
    vendor_type = serializers.SerializerMethodField()
    current_balance = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    currency = serializers.SerializerMethodField()
    
    class Meta:
        model = Vendor
        fields = '__all__'
        
    def get_vendor_type(self, obj):
        # Map vendor_category to vendor_type expected by frontend
        category_to_type = {
            'Supplier': 'supplier',
            'Contractor': 'contractor',
            'Service': 'business',
        }
        return category_to_type.get(obj.vendor_category, 'business')
    
    def get_current_balance(self, obj):
        # Default to 0 for now, can be calculated from transactions later
        return 0.0
    
    def get_status(self, obj):
        # Default to active for now
        return 'active'
    
    def get_currency(self, obj):
        # Use company default currency
        return 'USD'


class PurchaseOrderLineItemSerializer(serializers.ModelSerializer):
    """Serializer for Purchase Order Line Items"""
    
    class Meta:
        model = PurchaseOrderLineItem
        fields = '__all__'
        read_only_fields = ('purchase_order', 'line_total', 'tax_amount', 'quantity_pending')


class PurchaseOrderSerializer(serializers.ModelSerializer):
    """Serializer for Purchase Order model"""
    line_items = PurchaseOrderLineItemSerializer(many=True, required=False)
    vendor_name = serializers.CharField(source='vendor.name', read_only=True)
    
    class Meta:
        model = PurchaseOrder
        fields = '__all__'
        read_only_fields = ('po_id', 'po_number', 'subtotal', 'total_amount', 'balance_due', 'created_at', 'updated_at', 'created_by')
    
    def create(self, validated_data):
        line_items_data = validated_data.pop('line_items', [])
        validated_data['created_by'] = self.context['request'].user
        
        purchase_order = PurchaseOrder.objects.create(**validated_data)
        
        # Create line items
        for line_item_data in line_items_data:
            PurchaseOrderLineItem.objects.create(
                purchase_order=purchase_order,
                **line_item_data
            )
        
        # Recalculate totals after creating line items
        purchase_order.calculate_totals()
        purchase_order.save()
        
        return purchase_order
    
    def update(self, instance, validated_data):
        line_items_data = validated_data.pop('line_items', [])
        
        # Update purchase order fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update line items
        if line_items_data:
            # Delete existing line items
            instance.line_items.all().delete()
            
            # Create new line items
            for line_item_data in line_items_data:
                PurchaseOrderLineItem.objects.create(
                    purchase_order=instance,
                    **line_item_data
                )
            
            # Recalculate totals after updating line items
            instance.calculate_totals()
            instance.save()
        
        return instance


class VendorBillVendorSerializer(serializers.ModelSerializer):
    """Serializer for vendor details in vendor bills"""
    id = serializers.IntegerField(source='contact.id', read_only=True)
    display_name = serializers.CharField(source='contact.name', read_only=True)
    email = serializers.CharField(source='contact.email', read_only=True)
    phone = serializers.CharField(source='contact.phone', read_only=True)
    billing_address = serializers.SerializerMethodField()
    payment_terms = serializers.CharField(source='payment_terms', read_only=True)

    class Meta:
        model = Vendor
        fields = ['id', 'display_name', 'email', 'phone', 'billing_address', 'payment_terms']

    def get_billing_address(self, obj):
        # Combine address fields into a single string
        address_parts = []
        if obj.contact.address:
            address_parts.append(obj.contact.address)
        if obj.contact.city:
            address_parts.append(obj.contact.city)
        if obj.contact.state:
            address_parts.append(obj.contact.state)
        if obj.contact.postal_code:
            address_parts.append(obj.contact.postal_code)
        return ', '.join(address_parts) if address_parts else ''


class VendorBillItemSerializer(serializers.ModelSerializer):
    """Serializer for vendor bill line items"""
    product_name = serializers.CharField(source='product.name', read_only=True)

    class Meta:
        model = VendorBillItem
        fields = [
            'id', 'product', 'product_name', 'description', 'quantity',
            'unit_cost', 'line_total', 'tax_rate', 'tax_amount', 'line_order'
        ]
        read_only_fields = ['line_total', 'tax_amount']


class VendorBillSerializer(serializers.ModelSerializer):
    """Serializer for vendor bills"""
    vendor_details = VendorBillVendorSerializer(source='vendor', read_only=True)
    items = VendorBillItemSerializer(many=True, required=False)

    class Meta:
        model = VendorBill
        fields = [
            'id', 'bill_number', 'vendor', 'vendor_details', 'bill_date', 'due_date',
            'status', 'items', 'subtotal', 'tax_total', 'total_amount',
            'amount_paid', 'balance_due', 'reference_number', 'notes', 'terms',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['bill_number', 'subtotal', 'tax_total', 'total_amount', 'balance_due']

    def create(self, validated_data):
        items_data = validated_data.pop('items', [])
        vendor_bill = VendorBill.objects.create(**validated_data)

        # Create line items
        for item_data in items_data:
            VendorBillItem.objects.create(vendor_bill=vendor_bill, **item_data)

        # Calculate totals
        vendor_bill.calculate_totals()
        vendor_bill.save()

        return vendor_bill

    def update(self, instance, validated_data):
        items_data = validated_data.pop('items', None)

        # Update vendor bill fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update line items if provided
        if items_data is not None:
            # Delete existing line items
            instance.items.all().delete()

            # Create new line items
            for item_data in items_data:
                VendorBillItem.objects.create(vendor_bill=instance, **item_data)

            # Recalculate totals after updating line items
            instance.calculate_totals()
            instance.save()

        return instance